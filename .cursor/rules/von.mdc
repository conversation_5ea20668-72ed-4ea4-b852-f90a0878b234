---
description: 
globs: 
alwaysApply: true
---
---
description: 该规则解释了代码格式规范
alwaysApply: true
---

如果他原本的代码写的很差(不是说逻辑, 是风格很差), 请不要学习他的风格, 并用企业级的要求写代码, 另外全程中文交流



## 日志处理规范

1. **强制日志规则**:

   - 所有异常处理语句之前必须使用 `log.error()` 记录错误日志

   - 关键业务节点或流程开始前必须使用 `log.info()` 记录

## 循环操作规范

在企业级开发中,为了保证系统性能和稳定性,以下操作严禁在循环中进行:

1. 数据库操作

2. 远程调用

3. 文件操作

4. 并发并行

   

## 方法注释规范

所有新添加的方法必须添加完整的注释,包括但不限于:

1. 方法功能说明
2. 参数说明
3. 返回值说明

注释规范要求:

1. Controller层到Mapper层的所有方法都必须添加注释
2. 即使方法在不同层有相同的功能,也必须在每一层都添加完整注释
3. 注释格式参考:

```java
/**
 * 获取代理可用联系人类型下拉框（排除已选择的类型）
 *
 * @param fkAppAgentId 代理ID
 * @return 可用的联系人类型列表
 */
```



## 枚举类开发规范

### 1. 基本属性要求

- 每个枚举类必须至少包含两个基本属性:
  - `code`: 唯一标识(可以是String或Integer类型)
  - `msg`: 中文说明

- 可以根据业务需求适当增加其他属性

### 2. 必备方法

每个枚举类都必须包含以下内容:

1. 静态Map用于存储枚举实例:

```java
// 示例: 如果枚举类名为ContactPersonTypeEnum，则Map名为CONTACT_PERSON_TYPE_MAP
private static final Map<String, ContactPersonTypeEnum> CONTACT_PERSON_TYPE_MAP = new HashMap<>();

static {
    for (ContactPersonTypeEnum enumItem : ContactPersonTypeEnum.values()) {
        CONTACT_PERSON_TYPE_MAP.put(enumItem.getCode(), enumItem);
    }
}
```

2. 根据唯一标识获取枚举实例的方法:

```java
/**
 * 根据唯一标识获取对应的联系人类型枚举实例
 *
 * @param code 联系人类型
 * @return 对应的联系人类型枚举实例，如果找不到则返回null
 */
// 示例: 如果枚举类名为ContactPersonTypeEnum，则方法名为getContactPersonTypeByCode
public static ContactPersonTypeEnum getContactPersonTypeByCode(String code) {
    return CONTACT_PERSON_TYPE_MAP.get(code);
}
```

### 3. 注释规范

- 类注释: 简要说明枚举的用途
- 枚举值注释: 使用msg的值作为注释
- 属性和方法注释: 简洁明了地说明用途

### 4. 命名规范

- 枚举类名: 以Enum结尾，如`ContactPersonTypeEnum`
- 属性名: `code`, `msg`
- Map名: 格式为`${枚举名去掉Enum后缀大写}_MAP`，如`CONTACT_PERSON_TYPE_MAP`
- 方法名: 格式为`get${枚举名去掉Enum后缀}ByCode`，如`getContactPersonTypeByCode`

### 5. 通用方法

如果枚举类需要一些通用的业务处理方法，可以直接在枚举类中实现。这些方法应该：

- 与当前枚举的业务逻辑紧密相关
- 具有复用价值
- 方法名清晰表达其功能
- 包含完整的方法注释

### 6. 完整示例

```java
/**
 * 代理商联系人类型枚举
 *
 * <AUTHOR>
 * @Date 2025-06-27 15:36:00
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum ContactPersonTypeEnum {

    /**
     * 顾问（Counselor）
     */
    SALES("CONTACT_AGENT_SALES", "顾问（Counselor）", false),

    /**
     * 【New】紧急联系人（Emergency Contact）
     */
    EMERGENCY("CONTACT_AGENT_EMERGENCY", "【New】紧急联系人（Emergency Contact）", false);

    /**
     * 唯一标识码
     */
    private final String code;

    /**
     * 联系人类型的显示名称
     */
    private final String msg;

    /**
     * 是否为佣金邮件接收地址
     */
    private final Boolean defaultIsCommissionEmail;

    /**
     * 联系人枚举类型映射表
     */
    private static final Map<String, ContactPersonTypeEnum> CONTACT_PERSON_TYPE_MAP = new HashMap<>();

    static {
        for (ContactPersonTypeEnum contactPersonTypeEnum : ContactPersonTypeEnum.values()) {
            CONTACT_PERSON_TYPE_MAP.put(contactPersonTypeEnum.getCode(), contactPersonTypeEnum);
        }
    }

    /**
     * 根据唯一表示获取对应的联系人类型枚举实例
     *
     * @param code 联系人类型
     * @return 对应的联系人类型枚举实例，如果找不到则返回null
     */
    public static ContactPersonTypeEnum getContactPersonTypeByCode(String code) {
        return CONTACT_PERSON_TYPE_MAP.get(code);
    }

    /**
     * 获取指定联系人类型的默认佣金邮件接收设置
     *
     * @param code 联系人类型
     * @return true表示默认接收佣金邮件，false表示默认不接收
     */
    public static Boolean getDefaultIsCommissionEmail(String code) {
        ContactPersonTypeEnum typeEnum = getContactPersonTypeByCode(code);
        return typeEnum != null ? typeEnum.getDefaultIsCommissionEmail() : Boolean.FALSE;
    }

    /**
     * 判断给定的联系人类型是否为新增类型
     * 新增类型包括：企业管理员、佣金结算负责人、紧急联系人
     *
     * @param code 联系人类型
     * @return true表示是新增类型，false表示是传统类型
     */
    public static boolean isNewContactPersonType(String code) {
        if (Strings.isBlank(code)) {
            return false;
        }
        ContactPersonTypeEnum contactPersonTypeEnum = getContactPersonTypeByCode(code);
        switch (contactPersonTypeEnum) {
            case ADMIN:
            case COMMISSION:
            case EMERGENCY:
                return true;
            default:
                return false;
        }
    }

}
```

## 核心编码规范原则

### 1. 代码开发基本原则

- **参考已有实现**：
  - 开发新功能前，必须先研究现有相似功能的实现方式
  - 保持代码风格的一致性
  - 遵循项目既有的设计模式和架构决策
  - 复用已有的工具类和通用方法
  - 文件的存放位置参考他原来的文件存放位置
- **企业级标准**：
  - 所有代码必须符合企业级开发规范
  - 代码质量必须达到生产环境使用标准
  - 遵循最佳实践和设计模式
  - 考虑性能、安全性和可维护性
  - 符合团队协作和代码审查要求

#### 2.3 注释规范

- 类、方法、关键属性必须添加注释
- 复杂业务逻辑需要添加说明
- 注释要简洁清晰，避免废话
- 及时更新注释，确保与代码同步



## 代码复用与修改原则

### 1. 代码复用优先原则

- **优先使用现有功能**：
  - 开发新功能前，必须先检查是否已有可用的实现
  - 如果现有功能可以满足需求，直接使用而不是重新开发
  - 即使现有代码风格与规范不完全一致，只要功能正常也应优先使用

- **避免不必要的重构**：
  - 不要仅因代码风格与当前规范不一致就重构现有代码
  - 如果现有代码（如枚举类）能正常工作，不要强行按新规范重写

### 2. 代码修改原则

- **遵循开闭原则**：
  - 对扩展开放，对修改关闭
  - 优先通过添加新方法或属性来扩展功能
  - 不轻易修改原有代码的实现方式

- **保持原有代码风格(重点中的重点, 请没有我的要求下随意的改动他原有的代码风格这种无关紧要的东西, 因为)**：
  - 尊重原作者的代码风格和实现方式
  - 不随意改变原有代码的格式和结构
  - 只在明确要求时才修改原有代码

- **允许的修改范围**：
  - 可以添加新的方法或属性
  - 可以扩展现有功能
  - 可以修复明确的bug
  - 其他修改需要得到明确授权

### 3. 代码格式规范

- **空行使用规范**：

  - 类定义开始和结束处各空一行, 结束在大括号之前也要加空行, 请记住
  - 属性之间空一行
  - 方法之间空一行
  - 方法和属性之间空一行

- **参考示例**：

  ```java
  public class ExampleClass {
  
      private String field1;
  
      private String field2;
  
      private String field3;
  
      public void method1() {
          // method implementation
      }
  
      public void method2() {
          // method implementation
      }
  
      public void method3() {
          // method implementation
      }
    
  }
  ```

  

