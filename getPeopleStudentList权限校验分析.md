# getPeopleStudentList 权限校验控制详细分析

## 1. 方法概述

`getPeopleStudentList` 是 `MStudentServiceImpl` 中的核心方法，用于获取学生列表数据。该方法实现了严格的权限校验控制，确保用户只能查看有权限的学生数据。

## 2. 权限校验流程

### 2.1 Java 层权限检查 (resultParams 方法)

<augment_code_snippet path="src/main/java/com/partner/service/impl/MStudentServiceImpl.java" mode="EXCERPT">
````java
public MStudentParams resultParams(MStudentParamsDto dto) {
    MStudentParams params = new MStudentParams();
    BeanCopyUtils.copyProperties(dto, params);
    FzhUser fzhUser = SecurityUtils.getUser();
    UserInfoParams userinfo = UserInfoParamsUtils.getUserInfoParams(
        Long.parseLong(fzhUser.getFkTenantId()), 
        fzhUser.getFkFromPlatformCode(), 
        fzhUser.getId()
    );
````
</augment_code_snippet>

#### 权限检查逻辑：

1. **角色检查**：
   - 如果用户没有角色信息 (`roleIds` 为空)，设置 `studentFlag=true`，直接拒绝访问
   - 如果用户有角色但没有权限标识 (`permissionKeys` 为空)，同样拒绝访问

2. **权限标识检查**：
   - `STUDENT_VIEW_ALL`：全部学生查看权限，设置 `roleTypeFlag=false`
   - `STUDENT_VIEW_PERSON`：个人学生查看权限，设置 `roleTypeFlag=true`
   - 都不包含：拒绝访问，设置 `studentFlag=true`

### 2.2 主方法权限验证

<augment_code_snippet path="src/main/java/com/partner/service/impl/MStudentServiceImpl.java" mode="EXCERPT">
````java
@Override
public IPage getPeopleStudentList(Page page, MStudentParamsDto dto) {
    List<MStudentVo> resultStudents = new ArrayList<>();
    
    MStudentParams params = resultParams(dto);
    if (params.getStudentFlag()) {
        return new Page(); // 无权限直接返回空页面
    }
    
    List<MStudentStepList> studentList = mStudentMapper.getPeopleStudentList(params);
    // ... 后续数据处理
}
````
</augment_code_snippet>

## 3. SQL 层权限控制

### 3.1 权限过滤 SQL 片段

<augment_code_snippet path="app-partner/app-partner-biz/src/main/resources/mapper/PermissionSqlMapper.xml" mode="EXCERPT">
````xml
<sql id="offerItemPermissionRoleSql">
    INNER JOIN (
        SELECT DISTINCT offerItem.id AS offerItemId
        FROM ais_sale_center.m_student_offer_item AS offerItem
        
        <!-- 个人角色权限控制 -->
        <if test="roleTypeFlag==true">
            INNER JOIN (
                SELECT DISTINCT fk_student_id FROM app_partner_center.r_partner_user_student
                WHERE is_active=1 AND fk_partner_user_id =#{partnerUserId}
                UNION
                SELECT DISTINCT fk_student_id FROM ais_sale_center.m_app_student
                WHERE fk_platform_create_user_id=#{partnerUserId} 
                  AND fk_platform_code='PARTNER' AND status=2
            ) distributionStudent ON offerItem.fk_student_id = distributionStudent.fk_student_id
        </if>
        
        WHERE offerItem.status=1
          AND offerItem.is_follow=0
          AND offerItem.is_follow_hidden=0
          AND offerItem.fk_agent_id=#{agentId}
    ) permission ON offerItem.id = permission.offerItemId
</sql>
````
</augment_code_snippet>

### 3.2 权限控制机制详解

#### 个人权限模式 (roleTypeFlag=true)

当用户只有 `STUDENT_VIEW_PERSON` 权限时，系统通过 UNION 查询两种情况：

1. **分配权限**：通过 `r_partner_user_student` 表
   - 检查用户是否被分配了该学生
   - 条件：`is_active=1` 且 `fk_partner_user_id` 匹配当前用户

2. **创建权限**：通过 `m_app_student` 表
   - 检查用户是否是该学生申请的创建者
   - 条件：`fk_platform_create_user_id` 匹配且平台为 'PARTNER'，状态为已审核通过(status=2)

#### 全部权限模式 (roleTypeFlag=false)

当用户有 `STUDENT_VIEW_ALL` 权限时，不添加个人权限过滤，但仍需满足：
- 代理商权限：`fk_agent_id` 匹配用户所属代理商
- 数据有效性：`status=1`、`is_follow=0`、`is_follow_hidden=0`

## 4. 主查询 SQL 分析

<augment_code_snippet path="app-partner/app-partner-biz/src/main/resources/mapper/MStudentMapper.xml" mode="EXCERPT">
````xml
<select id="getPeopleStudentList" resultType="com.partner.vo.student.MStudentStepList">
    SELECT studentUuid.fk_student_uuid AS studentUUID,
           student.name AS studentName,
           offerItem.fk_institution_id AS institutionId,
           institution.name AS institutionNameChn,
           offerItemStep.step_name,
           institutionCourse.name AS courseName,
           uAreaCountry.name_chn AS countryName,
           (SELECT GROUP_CONCAT(mPartnerUser.name) 
            FROM app_partner_center.r_partner_user_student rPartnerUserStudent
            LEFT JOIN app_partner_center.m_partner_user mPartnerUser 
            ON rPartnerUserStudent.fk_partner_user_id=mPartnerUser.id
            WHERE rPartnerUserStudent.fk_student_id=student.id 
              AND rPartnerUserStudent.is_active=1
           ) AS partnerUserName
    FROM ais_sale_center.m_student_offer_item offerItem
    <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
    INNER JOIN ais_sale_center.m_student student ON offerItem.fk_student_id = student.id
    INNER JOIN ais_sale_center.r_student_uuid studentUuid ON studentUuid.fk_student_id=student.id
    INNER JOIN ais_institution_center.m_institution institution ON institution.id = offerItem.fk_institution_id
    INNER JOIN ais_sale_center.u_student_offer_item_step offerItemStep ON offerItemStep.id = offerItem.fk_student_offer_item_step_id
    LEFT JOIN ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id
    LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON offerItem.fk_area_country_id = uAreaCountry.id
    WHERE 1=1
    ORDER BY student.id DESC, offerItemStep.step_order ASC
</select>
````
</augment_code_snippet>

### 4.1 涉及的数据表

1. **核心业务表**：
   - `m_student_offer_item`：学生申请项目表（主表）
   - `m_student`：学生基本信息表
   - `r_student_uuid`：学生UUID映射表

2. **权限控制表**：
   - `r_partner_user_student`：用户学生分配关系表
   - `m_app_student`：应用学生申请表

3. **业务关联表**：
   - `m_institution`：院校信息表
   - `u_student_offer_item_step`：申请步骤表
   - `m_institution_course`：院校课程表
   - `u_area_country`：国家信息表

## 5. 数据处理流程

### 5.1 Java 层数据聚合

<augment_code_snippet path="src/main/java/com/partner/service/impl/MStudentServiceImpl.java" mode="EXCERPT">
````java
if (!ObjectUtil.isEmpty(studentList)) {
    Map<String, List<MStudentStepList>> groupbylist = 
        studentList.stream().collect(Collectors.groupingBy(MStudentStepList::getStudentUUID));
    
    Set studentids = new HashSet();
    for (MStudentStepList po : studentList) {
        if (studentids.contains(po.getStudentUUID())) {
            continue; // 去重处理
        }
        
        MStudentVo studentVo = new MStudentVo();
        studentVo.setStudentUUID(po.getStudentUUID());
        studentVo.setName(po.getStudentName());
        
        List<MStudentStepList> tmplist = groupbylist.get(po.getStudentUUID());
        if (tmplist != null && tmplist.size() >= 2) {
            List<MStudentStepList> studentStepTwoPairs = tmplist.subList(0, 2);
            studentVo.setStudentStepTwoPairs(studentStepTwoPairs);
        }
        
        studentids.add(po.getStudentUUID());
        resultStudents.add(studentVo);
    }
}
````
</augment_code_snippet>

### 5.2 内存分页处理

系统在 Java 层面实现分页，通过 `getPages` 方法对结果进行分页处理，这种方式在大数据量时可能存在性能问题。

## 6. 权限控制总结

### 6.1 三层权限控制架构

1. **应用层权限**：通过 `resultParams` 方法检查用户基础权限
2. **业务层权限**：通过 `roleTypeFlag` 控制数据访问范围
3. **数据层权限**：通过代理商ID确保数据隔离

### 6.2 权限类型

- **STUDENT_VIEW_ALL**：可查看代理商下所有学生数据
- **STUDENT_VIEW_PERSON**：只能查看分配给自己或自己创建的学生数据
- **无权限**：直接返回空结果

### 6.3 安全特性

1. **多重验证**：Java层 + SQL层双重权限验证
2. **数据隔离**：通过代理商ID确保不同代理商数据隔离
3. **状态控制**：只显示有效状态的数据
4. **审核控制**：只显示已审核通过的学生申请

这种权限控制机制确保了数据安全性，但在性能方面可能需要进一步优化，特别是在大数据量场景下。
