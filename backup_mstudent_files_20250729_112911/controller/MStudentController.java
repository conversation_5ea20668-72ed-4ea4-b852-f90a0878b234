package com.partner.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.partner.dto.base.CountryStateCityParamsDto;
import com.partner.dto.student.MStudentAddOrEditDto;
import com.partner.dto.student.MStudentSubAndAddOrEditDto;
import com.partner.dto.student.MStudentParamsDetailDto;
import com.partner.dto.student.MStudentParamsDto;
import com.partner.service.MStudentService;
import com.partner.service.impl.MStudentServiceImpl;
import com.partner.util.BeanCopyUtils;
import com.partner.vo.combox.StudentOfferItemCourtyCombox;
import com.partner.vo.combox.StudentOfferItemStepCombox;
import com.partner.vo.student.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Tag(description = "mstudent" , name = "小程序-学生管理" )
@RestController
@RequestMapping("/mstudent")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
public class MStudentController {

    private final MStudentService studentService;


    @Operation(summary = "学生管理-(按人或申请)查国家信息" , description = "学生管理-(按人)反查国家信息" )
    @SysLog("学生管理-(按人或申请)反查国家信息" )
    @GetMapping("/getCountryComboxPeopleOrStep" )
    public R getCountryComboxPeopleOrStep(@ParameterObject @Valid MStudentParamsDto dto){
        List<StudentOfferItemCourtyCombox> result=new ArrayList<>();
        if(dto.getSearchType()==0){
            result=studentService.getCountryComboxPeople(dto);
        } else if (dto.getSearchType()==1) {
            result=studentService.getCountryComboxApply(dto);
        }
        return R.ok(result);
    }




    @Operation(summary = "学生管理-(按申请)反查申请步骤" , description = "学生管理-(按申请)反查申请步骤" )
    @SysLog("学生管理-(按申请)反查申请步骤" )
    @GetMapping("/getAllStepOrderPeopleOrApplyNums" )
    public R getAllStepOrderApplyNums(@ParameterObject @Valid MStudentParamsDto dto){
        List<StudentOfferItemStepCombox> result=new ArrayList<>();
        result=studentService.getAllStepOrderApplyNums(dto);

        return R.ok(result);
    }

    @Operation(summary = "学生管理-(按人或申请)查询学生申请列表" , description = "学生管理-(按人或申请)查询学生申请列表" )
    @SysLog("学生管理-(按人或申请)查询学生申请列表" )
    @GetMapping("/getPeopleOrApplyStudents" )
    public R getPeopleOrApplyStudents(Page page, @ParameterObject @Valid MStudentParamsDto dto){
        IPage result=new Page();
        if(dto.getSearchType()==0){
            result=studentService.getPeopleStudentList(page,dto);
        } else if (dto.getSearchType()==1) {
            result=studentService.getApplyStudentList(page,dto);
        }
        return R.ok(result);
    }


    @Operation(summary = "学生管理-首页学生申请列表" , description = "学生管理-首页学生申请列表" )
    @SysLog("学生管理-首页学生申请列表" )
    @GetMapping("/getHomeStudents" )
    public R getHomeStudents(){
        List<MStudentHomeVo> result=studentService.getHomeStudents();
        return R.ok(result);
    }



    @Operation(summary = "学生管理-详情" , description = "学生管理-详情" )
    @SysLog("学生管理-详情" )
    @GetMapping("/getApplyStudentsDetail" )
    public R getApplyStudentsDetail(@ParameterObject @Valid MStudentParamsDetailDto dto){
        StudentDetailInfoVo resultvo=new StudentDetailInfoVo();
        resultvo=studentService.getApplyStudentsDetail(dto);

        return R.ok(resultvo);
    }
    @Operation(summary = "学生管理-详情-申请详情" , description = "学生管理-详情-申请详情" )
    @SysLog("学生管理-申请详情" )
    @GetMapping("/getApplyDetail" )
    public R getApplyDetail(@ParameterObject @Valid MStudentParamsDetailDto dto){
        StudentOfferItemDetailVo result=studentService.getApplyDetail(dto);
        return R.ok(result);
    }


    @Operation(summary = "学生统计-学生进度汇总" , description = "学生统计-学生进度汇总" )
    @SysLog("学生管理-学生进度汇总" )
    @GetMapping("/getApplyStudentsPlanSum" )
    public R getApplyStudentsPlanSum(@ParameterObject  MStudentParamsDto dto){
        StudentPlanSumVo result=new StudentPlanSumVo();
        result=studentService.getApplyStudentsPlanSum(dto);
        return R.ok(result);
    }

    @Operation(summary = "首页-本月数据" , description = "首页-本月数据" )
    @SysLog("首页-本月数据" )
    @GetMapping("/getMonthStudentsApplySum" )
    public R getMonthStudentsApplySum(@ParameterObject  MStudentParamsDto dto){
        StudentMonthPlanSumVo result=new StudentMonthPlanSumVo();
        result=studentService.getMonthStudentsApplySum(dto);
        return R.ok(result);
    }

    @Operation(summary = "学生统计-本月统计数量" , description = "学生统计-本月统计申请计划数量" )
    @SysLog("学生管理-本月统计数量" )
    @GetMapping("/getMonthNewSum" )
    public R getMonthNewSum(){
        StudentPlanSumMonthVo result= studentService.getMonthNewSum();
        return R.ok(result);
    }

    @Operation(summary = "学生统计-学生申请国家" , description = "学生统计-学生申请国家" )
    @SysLog("学生管理-学生申请国家" )
    @GetMapping("/getApplyStudentsCountry" )
    public R getApplyStudentsCountry(@ParameterObject  MStudentParamsDto dto){

        StudentApplySumVo result =studentService.getApplyStudentsCountry(dto);
        return R.ok(result);
    }
    @Operation(summary = "学生统计-月度汇总" , description = "学生统计-月度汇总" )
    @SysLog("学生统计-月度汇总" )
    @GetMapping("/getMonthApplayTotal" )
    public R getMonthApplayTotal(@ParameterObject MStudentParamsDto dto){
        Map<String,List<StudentMonthOfferVo>> result= studentService.getMonthApplayTotal(dto);


        return R.ok(result);
    }

    @Operation(summary = "学生统计-学生申请学校排名" , description = "学生统计-学生申请学校排名" )
    @SysLog("学生统计-学生申请学校排名" )
    @GetMapping("/getStudentsApplyInstitutionRanking" )
    public R getStudentsApplyInstitutionRanking(@ParameterObject @Valid MStudentParamsDto dto){
        List<StudentApplyRankingVo> resultRanking=new ArrayList<>();
        resultRanking=studentService.getStudentsApplyInstitutionRanking(dto);

        return R.ok(resultRanking);
    }


    @Operation(summary = "学生添加-保存草稿" , description = "学生添加-保存草稿" )
    @SysLog("学生添加-新增" )
    @PostMapping("/addStudents" )
    public R addStudents(@RequestBody  @Validated(MStudentAddOrEditDto.Add.class) MStudentAddOrEditDto dto){
        dto.setSaveType(0);
        studentService.addStudents(dto);
        return R.ok();
    }

    @Operation(summary = "学生添加-修改草稿" , description = "学生添加-修改草稿" )
    @SysLog("学生添加-修改" )
    @PostMapping("/editStudents" )
    public R editStudents(@RequestBody  @Validated(MStudentAddOrEditDto.Update.class) MStudentAddOrEditDto dto){
        dto.setSaveType(0);
        studentService.editStudents(dto);
        return R.ok();
    }



    @Operation(summary = "学生添加-保存且提交" , description = "学生添加-保存且提交" )
    @SysLog("学生添加-保存且提交" )
    @PostMapping("/addAndSubStudents" )
    public R addAndSubStudents(@RequestBody  @Validated(MStudentAddOrEditDto.Add.class) MStudentSubAndAddOrEditDto dto){
        MStudentAddOrEditDto params= BeanCopyUtils.objClone(dto,MStudentAddOrEditDto::new);
        params.setSaveType(1);
        studentService.addStudents(params);
        return R.ok();
    }

    @Operation(summary = "学生添加-修改且提交" , description = "学生添加-修改且提交" )
    @SysLog("学生添加-修改且提交" )
    @PostMapping("/editAndSubStudents" )
    public R editAndSubStudents(@RequestBody  @Validated(MStudentAddOrEditDto.Update.class) MStudentSubAndAddOrEditDto dto){
        MStudentAddOrEditDto params= BeanCopyUtils.objClone(dto,MStudentAddOrEditDto::new);
        params.setSaveType(1);
        studentService.editStudents(params);
        return R.ok();
    }


    @Operation(summary = "小程序-国家下拉框(顾问按业务国别过滤)" , description = "小程序-国家下拉框(顾问按业务国别过滤)" )
    @SysLog("小程序-国家下拉框" )
    @GetMapping("/getCountryCombox" )
    public R getCountryCombox(@ParameterObject CountryStateCityParamsDto dto){
        return R.ok(studentService.getCountryCombox(dto));
    }

    @Operation(summary = "小程序-提交学生(新增/修改)校验" , description = "小程序-提交学生(新增/修改)校验" )
    @SysLog("小程序-提交学生(新增/修改)校验" )
    @PostMapping("/getValidStudents" )
    public R getValidStudents(@RequestBody  @Validated MStudentSubAndAddOrEditDto dto){

        return R.ok(studentService.getValidStudents(dto));
    }

    // ============================= 优化版本接口方法 =============================
    
    @Operation(summary = "【优化版本】学生管理-(按人或申请)查询学生列表" , description = "优化版本：按人查询使用优化算法，按申请查询使用原算法" )
    @SysLog("【优化版本】学生管理-(按人或申请)查询学生列表" )
    @GetMapping("/getPeopleStudentListOptimized" )
    public R getPeopleStudentListOptimized(Page page, @ParameterObject @Valid MStudentParamsDto dto){
        IPage result = new Page();
        
        if(dto.getSearchType() == 0){
            // 按人查询 - 使用优化版本
            result = studentService.getPeopleStudentListOptimized(page, dto);
        } else if (dto.getSearchType() == 1) {
            // 按申请查询 - 调用原方法
            result = studentService.getApplyStudentList(page, dto);
        }
        
        return R.ok(result);
    }


}
