package com.partner.dto.base;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "学校查询基础接口")
public class InstitutionSearchDto {

    @Schema(description="国家ID")
    Long areaCountryId;

    @Schema(description = "学校名称")
    private String institutionName;

    @Schema(description="单位CompanyId")
    Long fkCompanyId;

}
