package com.partner.dto.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


@Data
@Schema(description = "提交结算申请-签字文件参数")
public class MFileSettlementInfo {
    @NotNull(message = "文件Id不能为空")
    @Schema(description = "文件Id")
    private Long id;

    @NotBlank(message = "文件guid不能为空")
    @Schema(description = "文件guid")
    private String fileGuid;

    @NotBlank(message = "源文件类型不能为空")
    @Schema(description = "源文件类型")
    private String fileTypeOrc;

    @NotBlank(message = "源源文件名不能为空")
    @Schema(description = "源文件名")
    private String fileNameOrc;

    @NotBlank(message = "目标文件名不能为空")
    @Schema(description = "目标文件名")
    private String fileName;

    @NotBlank(message = "目标文件路径不能为空")
    @Schema(description = "目标文件路径")
    private String filePath;

    @NotBlank(message = "文件外部存储Key不能为空")
    @Schema(description = "文件外部存储Key（如：腾讯云COS）")
    private String fileKey;
}
