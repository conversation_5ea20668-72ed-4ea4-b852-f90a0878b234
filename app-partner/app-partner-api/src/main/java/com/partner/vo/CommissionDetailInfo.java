package com.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class CommissionDetailInfo implements Serializable {

    @Schema(description="佣金=年度")
    private int year;
    @Schema(description="佣金-标题(课程名称)")
    private String title;
    @Schema(description="课程等级")
    private String levelName;
    @Schema(description="适用国籍")
    private String national;

    @Schema(description="佣金")
    private String commission;


    @Schema(description="代理佣金")
    private String agentCommission;
    @Schema(description="代理佣金说明")
    private String agentCommissionNote;
    @Schema(description="后续佣金")
    private String followCommission;
    @Schema(description="后续佣金说明")
    private String followCommissionNote;

    private String agentFollowCommission;

    private String agentFollowCommissionNote;

    public String getLevelName() {
        if(levelName==null || "null".equals(levelName) || "Null".equals(levelName)){
            levelName="";
        }
        return levelName;
    }

    public String getNational() {
        if(national==null || "null".equals(national) || "Null".equals(national)){
            national="";
        }
        return national;
    }
}
