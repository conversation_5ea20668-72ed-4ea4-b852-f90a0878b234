package com.partner.vo.student;

import lombok.Data;
import java.io.Serializable;

/**
 * 学生核心查询信息VO - 优化版本
 * 用于简化的主查询，只包含核心字段，避免复杂JOIN
 * 
 * <AUTHOR>
 * @since 2025-01-28
 */
@Data
public class MStudentCoreInfoVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * offer item ID
     */
    private Long offerItemId;
    
    /**
     * 学生ID
     */
    private Long studentId;
    
    /**
     * 学生UUID
     */
    private String studentUUID;
    
    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 学生申请ID
     */
    private Long studentOfferId;

    /**
     * 学校ID
     */
    private Long institutionId;
    
    /**
     * 国家ID
     */
    private Long countryId;
    
    /**
     * 步骤ID
     */
    private Long stepId;
    
    /**
     * 步骤名称
     */
    private String stepName;
    
    /**
     * 步骤顺序
     */
    private Integer stepOrder;
    
    /**
     * 课程ID
     */
    private Long courseId;
    
    /**
     * offer item UUID
     */
    private String offerItemUUID;
    
    /**
     * 步骤时间
     */
    private String itemStepTime;
}