package com.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Schema(description = "返回活动信息")
public class EventPageVo {

    @Schema(description="活动Id")
    private Long id;

    /**
     * 活动编号
     */
    @Schema(description = "活动编号")
    private String num;
    /**
     * 活动时间
     */
    @Schema(description = "活动时间")
    private Date eventTime;
    /**
     * 活动结束时间
     */
    @Schema(description = "活动结束时间")
    private Date eventTimeEnd;
    /**
     * 活动主题
     */
    @Schema(description = "活动主题")
    private String eventTheme;


    @Schema(description = "活动举办国家Id")
    private Long fkAreaCountryIdHold;

    @Schema(description = "活动举办国家名称")
    private String areaCountryName;


    @Schema(description = "活动举办州省Id")
    private Long fkAreaStateIdHold;

    @Schema(description = "活动举办州省名称")
    private String areaStateName;

    @Schema(description = "活动举办城市Id")
    private Long fkAreaCityIdHold;

    @Schema(description = "活动举办城市名称")
    private String areaCityName;

    @Schema(description = "活动内容")
    private String remark;

    @Schema(description = "comment")
    private String comment;

    @Schema(description = "logo")
    private String fileKey;
    @Schema(description = "多文件")
    private String eventFile;

    @Schema(description = "当前用户活动报名id")
    private Long mEventRegistrationId;

    @Schema(description = "当前用户报名状态：0未报名 1已报名")
    private int registrationType;

    @Schema(description = "已报名数")
    private int regisPeopleCount;

    @Schema(description = "头像-用户信息")
    private List<AppointmentInfo> userinfo;

    @Schema(description = "活动类型名称")
    private String typeName;

    @Schema(description = "状态：0计划/1结束/2取消")
    private Integer status;

    public int getRegistrationType() {
        if(mEventRegistrationId!=null){
            registrationType=1;
        }
        return registrationType;
    }
}
