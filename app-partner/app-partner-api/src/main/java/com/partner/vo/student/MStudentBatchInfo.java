package com.partner.vo.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 学生批量查询信息VO - 用于关联数据批量查询
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "学生批量查询信息")
public class MStudentBatchInfo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "中文名称")
    private String nameChn;

    @Schema(description = "英文名称")
    private String nameEn;

    @Schema(description = "步骤名称")
    private String stepName;

    @Schema(description = "步骤排序")
    private Integer stepOrder;

    @Schema(description = "关联的学生ID或其他外键ID")
    private Long relatedId;

    @Schema(description = "用户名称列表（逗号分隔）")
    private String userNames;

    @Schema(description = "用户英文名称列表（逗号分隔）")
    private String userNamesEn;
}
