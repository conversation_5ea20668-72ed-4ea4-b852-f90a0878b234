package com.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 团队架构树
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TeamMemberTreeVo {
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "伙伴用户ID")
    private Long partnerUserId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色Code")
    private String roleCode;

    @Schema(description = "子菜单")
    private List<TeamMemberTreeVo> children;

    public TeamMemberTreeVo(Long userId, Long partnerUserId, String name) {
        this.userId = userId;
        this.partnerUserId = partnerUserId;
        this.name = name;
        this.children = new ArrayList<>();

    }

    public TeamMemberTreeVo(Long userId, Long partnerUserId, String name, String roleName, String roleCode) {
        this.userId = userId;
        this.partnerUserId = partnerUserId;
        this.name = name;
        this.roleName = roleName;
        this.roleCode = roleCode;
        this.children = new ArrayList<>();

    }

}
