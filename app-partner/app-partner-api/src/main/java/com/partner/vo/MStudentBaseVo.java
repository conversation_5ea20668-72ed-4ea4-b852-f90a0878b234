package com.partner.vo;

import com.partner.entity.MAppStudentEntity;
import com.partner.entity.MAppStudentOfferItemEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


@Data
public class MStudentBaseVo extends MAppStudentEntity {

    @Schema(description = "学生UUID")
    private String studentUUID;
    @Schema(description = "学生附件")
    List<FileArray> fileArray;


    @Schema(description = "申请计划")
    private List<MAppStudentOfferItemVo> list;



}
