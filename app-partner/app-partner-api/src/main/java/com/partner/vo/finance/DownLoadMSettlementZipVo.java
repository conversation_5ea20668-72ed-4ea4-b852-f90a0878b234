package com.partner.vo.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DownLoadMSettlementZipVo {
    @Schema(description = "结算账单Id")
    private Long id;


    @Schema(description="文件ID")
    private Long fileId;


    @Schema(description="附件")
    private String fileGuid;


    @Schema(description="附件-桶地址")
    private String fileKey;


    @Schema(description="源文件名")
    private String fileNameOrc;



    @Schema(description="代理名称")
    private String agentName;




}
