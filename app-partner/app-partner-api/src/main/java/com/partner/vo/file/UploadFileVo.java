package com.partner.vo.file;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/7/18
 * @Version 1.0
 * @apiNote:文件上传返回参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UploadFileVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "文件guid")
    private String fileGuid;

    @Schema(description = "源文件类型")
    private String fileTypeOrc;

    @Schema(description = "源文件名")
    private String fileNameOrc;

    @Schema(description = "目标文件名")
    private String fileName;

    @Schema(description = "目标文件路径")
    private String filePath;

    @Schema(description = "文件外部存储Key（如：腾讯云COS）")
    private String fileKey;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;
}
