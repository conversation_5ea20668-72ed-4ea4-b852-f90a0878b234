package com.partner.vo.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class MSettlementBillVo  {
    @Schema(description = "对账单ID")
    private Long msettlementId;
    @Schema(description = "对账单金额")
    private BigDecimal amount;
    @Schema(description = "申请状态集合")
    private String statusSettlements;


    @Schema(description = "学生数量")
    private int studentNum;
    @Schema(description = "申请时间")
    private LocalDate gmtCreate;


    @Schema(description = "申请状态")
    private Integer statusSettlement;

    @Schema(description = "申请状态名称")
    private String statusName;

    public String getStatusName() {
        String statusSettlements_TMP=getStatusSettlements();
            if(statusSettlements_TMP!=null && statusSettlements_TMP.length()>0){

                if(statusSettlements_TMP.indexOf("2")!=-1){
                    statusName="待审核";
                } else if (statusSettlements_TMP.indexOf("3")!=-1) {
                    statusName="结算中";
                } else if (statusSettlements_TMP.indexOf("4")!=-1) {
                    statusName="已完成";
                }else if(statusSettlements_TMP.indexOf("0")!=-1 || statusSettlements_TMP.indexOf("1")!=-1){
                    statusName="账单异常";
                }
            }else{

            }
        return statusName;
    }
}
