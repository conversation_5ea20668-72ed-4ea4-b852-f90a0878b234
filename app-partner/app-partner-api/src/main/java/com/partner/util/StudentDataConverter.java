package com.partner.util;

import com.partner.vo.student.*;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 学生数据转换工具类 - 优化版本
 * 负责将核心查询数据转换为业务VO对象
 * 
 * <AUTHOR>
 * @since 2025-01-28
 */
public class StudentDataConverter {
    
    /**
     * 步骤显示的最大数量
     */
    private static final int MAX_STEP_PAIRS = 2;
    
    /**
     * 构建学生VO对象
     *
     * @param studentUUID 学生UUID
     * @param stepList 步骤列表
     * @param institutionMap 学校信息映射
     * @param countryMap 国家信息映射
     * @param partnerUserMap Partner用户信息映射
     * @return 学生VO对象
     */
    public static MStudentVo buildStudentVo(String studentUUID,
                                           List<MStudentCoreInfoVO> stepList,
                                           Map<Long, InstitutionInfoVO> institutionMap,
                                           Map<Long, CourseInfoVO> courseMap,
                                           Map<Long, CountryInfoVO> countryMap,
                                           Map<Long, List<PartnerUserInfoVO>> partnerUserMap) {

        if (CollectionUtils.isEmpty(stepList)) {
            return null;
        }

        MStudentCoreInfoVO first = stepList.get(0);
        MStudentVo vo = new MStudentVo();

        // 基础信息
        vo.setStudentUUID(studentUUID);
        vo.setName(first.getStudentName());

        // 国家信息
        CountryInfoVO country = countryMap.get(first.getCountryId());
        if (country != null) {
            vo.setCountryName(country.getNameChn());
        }

        // Partner用户信息 - 修复：优先使用英文名，与原版本逻辑一致
        List<PartnerUserInfoVO> partnerUsers = partnerUserMap.get(first.getStudentId());
        String followName = buildFollowName(partnerUsers);
        vo.setFollowName(followName);

        // Step步骤信息处理 - 传递Partner用户信息以便在每个步骤中设置
        List<MStudentStepList> studentStepList = convertToStepList(stepList, institutionMap, courseMap, partnerUserMap);
        vo.setStudentStep(studentStepList);

        // 前两个步骤信息
        if (studentStepList.size() >= MAX_STEP_PAIRS) {
            vo.setStudentStepTwoPairs(studentStepList.subList(0, MAX_STEP_PAIRS));
        } else {
            vo.setStudentStepTwoPairs(studentStepList);
        }

        return vo;
    }

    /**
     * 构建followName - 与原版本逻辑完全一致
     * 原版本逻辑：优先使用partnerUserNameEn，如果为空则使用partnerUserName
     *
     * @param partnerUsers Partner用户列表
     * @return followName字符串
     */
    private static String buildFollowName(List<PartnerUserInfoVO> partnerUsers) {
        if (CollectionUtils.isEmpty(partnerUsers)) {
            return "";
        }

        // 模拟原版本的GROUP_CONCAT逻辑
        // 原版本SQL: GROUP_CONCAT(ifnull(mPartnerUser.name_en,mPartnerUser.name))
        // 这意味着优先使用name_en，如果为空则使用name
        return partnerUsers.stream()
            .map(user -> {
                String nameEn = user.getNameEn();
                // 注意：原版本SQL中已经用IFNULL处理了，所以nameEn不会为null
                // 但为了安全起见，我们还是检查一下
                return (nameEn != null && !nameEn.trim().isEmpty()) ? nameEn : user.getName();
            })
            .collect(Collectors.joining(","));
    }
    
    /**
     * 转换为步骤列表 - 修复字段映射和Partner用户信息
     */
    private static List<MStudentStepList> convertToStepList(List<MStudentCoreInfoVO> stepList,
                                                           Map<Long, InstitutionInfoVO> institutionMap,
                                                           Map<Long, CourseInfoVO> courseMap,
                                                           Map<Long, List<PartnerUserInfoVO>> partnerUserMap) {
        if (CollectionUtils.isEmpty(stepList)) {
            return Collections.emptyList();
        }

        return stepList.stream().map(core -> {
            MStudentStepList step = new MStudentStepList();

            // 基础信息
            step.setStudentUUID(core.getStudentUUID());
            step.setStudentName(core.getStudentName());
            step.setStudentOfferId(core.getStudentOfferId());
            step.setStepid(core.getStepId());
            step.setStepName(core.getStepName());
            step.setStepOrder(core.getStepOrder());
            step.setItemStepTime(core.getItemStepTime());
            step.setOfferItemUUID(core.getOfferItemUUID());

            // 学校信息 - 修复字段映射
            // 原版本SQL映射：
            // institution.name AS institutionNameChn (英文名映射到institutionNameChn)
            // institution.name_chn AS institutionName (中文名映射到institutionName)
            InstitutionInfoVO institution = institutionMap.get(core.getInstitutionId());
            if (institution != null) {
                step.setInstitutionNameChn(institution.getName());     // 英文名
                step.setInstitutionName(institution.getNameChn());     // 中文名
            }

            // 课程信息 - 单独查询
            CourseInfoVO course = courseMap.get(core.getCourseId());
            if (course != null) {
                step.setCourseName(course.getCourseName());
            }

            // Partner用户信息 - 设置到每个步骤中，与原版本一致
            List<PartnerUserInfoVO> partnerUsers = partnerUserMap.get(core.getStudentId());
            if (!CollectionUtils.isEmpty(partnerUsers)) {
                // 设置partnerUserName (中文名的GROUP_CONCAT)
                String partnerUserName = partnerUsers.stream()
                    .map(PartnerUserInfoVO::getName)
                    .collect(Collectors.joining(","));
                step.setPartnerUserName(partnerUserName);

                // 设置partnerUserNameEn (英文名优先的GROUP_CONCAT)
                String partnerUserNameEn = partnerUsers.stream()
                    .map(user -> {
                        String nameEn = user.getNameEn();
                        return (nameEn != null && !nameEn.trim().isEmpty()) ? nameEn : user.getName();
                    })
                    .collect(Collectors.joining(","));
                step.setPartnerUserNameEn(partnerUserNameEn);
            } else {
                step.setPartnerUserName("");
                step.setPartnerUserNameEn("");
            }

            return step;
        }).collect(Collectors.toList());
    }
    
    /**
     * 将InstitutionInfoVO列表转换为Map
     */
    public static Map<Long, InstitutionInfoVO> toInstitutionMap(List<InstitutionInfoVO> institutions) {
        if (CollectionUtils.isEmpty(institutions)) {
            return Collections.emptyMap();
        }
        
        return institutions.stream()
            .collect(Collectors.toMap(
                InstitutionInfoVO::getInstitutionId, 
                institution -> institution,
                (existing, replacement) -> existing
            ));
    }
    
    /**
     * 将CountryInfoVO列表转换为Map
     */
    public static Map<Long, CountryInfoVO> toCountryMap(List<CountryInfoVO> countries) {
        if (CollectionUtils.isEmpty(countries)) {
            return Collections.emptyMap();
        }
        
        return countries.stream()
            .collect(Collectors.toMap(
                CountryInfoVO::getCountryId, 
                country -> country,
                (existing, replacement) -> existing
            ));
    }
    
    /**
     * 将CourseInfoVO列表转换为Map
     */
    public static Map<Long, CourseInfoVO> toCourseMap(List<CourseInfoVO> courses) {
        if (CollectionUtils.isEmpty(courses)) {
            return Collections.emptyMap();
        }

        return courses.stream()
            .collect(Collectors.toMap(
                CourseInfoVO::getCourseId,
                course -> course,
                (existing, replacement) -> existing
            ));
    }

    /**
     * 将PartnerUserInfoVO列表转换为按学生ID分组的Map
     */
    public static Map<Long, List<PartnerUserInfoVO>> toPartnerUserMap(List<PartnerUserInfoVO> partnerUsers) {
        if (CollectionUtils.isEmpty(partnerUsers)) {
            return Collections.emptyMap();
        }

        return partnerUsers.stream()
            .collect(Collectors.groupingBy(PartnerUserInfoVO::getStudentId));
    }
}