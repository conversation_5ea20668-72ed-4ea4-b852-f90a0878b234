<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.PermissionSqlMapper">

    <!-- 申请计划维度的权限 -->
    <sql id="offerItemPermissionSql">
        INNER JOIN (
            SELECT DISTINCT offerItem.id AS offerItemId
            FROM ais_sale_center.m_student_offer_item AS offerItem
            <!--【顾问】过滤条件，自己及下属被分配的学生  -->
            <if test="(roleCode!=null and roleCode!='' and (roleCode=='COUNSELOR' || roleCode=='COPYWRINTING') )
             || roleFlag==true">
                INNER JOIN (
                SELECT DISTINCT fk_student_id FROM app_partner_center.r_partner_user_student WHERE is_active=1 AND fk_partner_user_id IN
                <!-- 自己+下属Id-->
                <foreach collection="levelPartnerUserIds" item="partUserId" index="index" open="(" separator="," close=")">
                   #{partUserId}
               </foreach>

               ) distributionStudent ON offerItem.fk_student_id = distributionStudent.fk_student_id

           </if>
        <!-- 【顾问】过滤条件-->
        WHERE offerItem.`status`=1
        AND offerItem.is_follow=0
        AND offerItem.is_follow_hidden=0
        AND offerItem.fk_agent_id=#{agentId}
    ) permission ON offerItem.id = permission.offerItemId


    </sql>

    <sql id="offerItemPermissionSqlPage">
        INNER JOIN (
        SELECT DISTINCT offerItem.id AS offerItemId
        FROM ais_sale_center.m_student_offer_item AS offerItem

        <!--【顾问】过滤条件，自己及下属被分配的学生  -->
        <if test="(query.roleCode!=null and query.roleCode!='' and (query.roleCode=='COUNSELOR' || query.roleCode=='COPYWRINTING') )
         || query.roleFlag==true">
            INNER JOIN (
            SELECT DISTINCT fk_student_id FROM app_partner_center.r_partner_user_student WHERE is_active=1 AND fk_partner_user_id IN
            <!-- 自己+下属Id-->
            <foreach collection="query.levelPartnerUserIds" item="partUserId" index="index" open="(" separator="," close=")">
                #{partUserId}
            </foreach>

            ) distributionStudent ON offerItem.fk_student_id = distributionStudent.fk_student_id

        </if>
        <!-- 【顾问】过滤条件-->
        WHERE offerItem.`status`=1
        AND offerItem.is_follow=0
        AND offerItem.fk_agent_id=#{query.agentId}
        ) permission ON offerItem.id = permission.offerItemId


    </sql>

    <sql id="partNerUserPermissionSql">
        <if test="(roleCode!=null and roleCode!='' and (roleCode=='COUNSELOR' || roleCode=='COPYWRINTING') )
             || roleFlag==true">
            AND id IN
            <!-- 自己+下属Id-->
            <foreach collection="levelPartnerUserIds" item="partUserId" index="index" open="(" separator="," close=")">
                #{partUserId}
            </foreach>
        </if>
    </sql>






    <!-- 按角色权限过滤查询 -->
    <sql id="offerItemPermissionRoleSql">
        INNER JOIN (
        SELECT DISTINCT offerItem.id AS offerItemId
        FROM ais_sale_center.m_student_offer_item AS offerItem
        <!--个人角色配置权限类型  -->
        <if test="roleTypeFlag==true">
            INNER JOIN (
            SELECT DISTINCT fk_student_id FROM app_partner_center.r_partner_user_student
            WHERE is_active=1 AND fk_partner_user_id IN
            <foreach collection="levelPartnerUserIds" item="partUserId" index="index" open="(" separator="," close=")">
               #{partUserId}
           </foreach>
            UNION
            SELECT  DISTINCT fk_student_id FROM  ais_sale_center.m_app_student
                WHERE fk_platform_create_user_id IN
                <foreach collection="levelPartnerUserIds" item="partUserId" index="index" open="(" separator="," close=")">
                   #{partUserId}
               </foreach>
                AND fk_platform_code='PARTNER' AND status=2

            ) distributionStudent ON offerItem.fk_student_id = distributionStudent.fk_student_id
        </if>
        <!-- 角色配置权限类型-->
        WHERE offerItem.`status`=1
        AND offerItem.is_follow=0
        AND offerItem.is_follow_hidden=0
        AND offerItem.fk_agent_id=#{agentId}
        ) permission ON offerItem.id = permission.offerItemId
    </sql>



    <sql id="offerItemPermissionSqlRolePage">
        INNER JOIN (
        SELECT DISTINCT offerItem.id AS offerItemId
        FROM ais_sale_center.m_student_offer_item AS offerItem

        <!--个人角色配置权限类型  -->
        <if test="query.roleTypeFlag==true">
            INNER JOIN (
                SELECT DISTINCT fk_student_id FROM app_partner_center.r_partner_user_student
                WHERE is_active=1 AND fk_partner_user_id =#{query.partnerUserId}
                UNION
                SELECT  DISTINCT fk_student_id FROM  ais_sale_center.m_app_student
                    WHERE fk_platform_create_user_id=#{query.partnerUserId} AND fk_platform_code='PARTNER' AND status=2
            ) distributionStudent ON offerItem.fk_student_id = distributionStudent.fk_student_id

        </if>
        <!-- 个人角色配置权限类型-->
        WHERE  offerItem.`status`=1
        AND offerItem.is_follow=0
        AND offerItem.fk_agent_id=#{query.agentId}
        ) permission ON offerItem.id = permission.offerItemId
    </sql>


</mapper>