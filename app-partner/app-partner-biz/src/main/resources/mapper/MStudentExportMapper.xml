<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MStudentExportMapper">



    <select id="getStudentsExport" resultType="com.partner.vo.student.MStudentExportVo">
        SELECT
               uStudentOfferItemStep.step_name AS startStepName,
               uStudentOfferItemStep2.step_name AS endStepName,
               mStudent.name,
               mStudent.last_name,
               mStudent.first_name,
               uAreaCountry.name countryName,
               uAreaCountry.name_chn countryNameChn,
               mInstitution.name_chn institutionNameChn,
               mInstitution.name institutionName,
               mInstitutionCourse.name courseName,
               mStudentOfferItem.opening_time openingTime,
               mStudent.gender,
               mStudent.birthday,
               mStudent.mobile,
               mStudent.email,
               mAgent.name AS  agentName,
               uAreaCountry1.name countryNameSuccess,
               uAreaCountry1.name_chn countryNameChnSuccess,
               mInstitution1.name_chn institutionNameChnSuccess,
               mInstitution1.name institutionNameSuccess,
               mInstitutionCourse1.name courseNameSuccess,
               mStudentOfferItem.gmt_create AS receivedApplicationDataDate


               FROM  ais_sale_center.m_student mStudent
               INNER JOIN ais_sale_center.m_student_offer_item mStudentOfferItem ON mStudent.id=mStudentOfferItem.fk_student_id
               <!-- 权限控制逻辑：支持下级权限（与查询功能保持一致） -->
               <if test="roleTypeFlag==true">
               INNER JOIN (
                   SELECT DISTINCT fk_student_id
                   FROM app_partner_center.r_partner_user_student
                   WHERE is_active=1 AND fk_partner_user_id IN
                   <foreach collection="levelPartnerUserIds" item="partUserId" open="(" separator="," close=")">
                       #{partUserId}
                   </foreach>
                   AND fk_tenant_id = #{tenantId}
                   UNION
                   SELECT DISTINCT fk_student_id
                   FROM ais_sale_center.m_app_student
                   WHERE fk_platform_create_user_id IN
                   <foreach collection="levelPartnerUserIds" item="partUserId" open="(" separator="," close=")">
                       #{partUserId}
                   </foreach>
                   AND fk_platform_code='PARTNER' AND status=2
               ) distributionStudent ON mStudentOfferItem.fk_student_id = distributionStudent.fk_student_id
               </if>
               LEFT  JOIN ais_institution_center.u_area_country uAreaCountry ON mStudentOfferItem.fk_area_country_id = uAreaCountry.id
               LEFT  JOIN ais_institution_center.m_institution mInstitution ON mStudentOfferItem.fk_institution_id = mInstitution.id
               LEFT  JOIN ais_institution_center.m_institution_course mInstitutionCourse ON mStudentOfferItem.fk_institution_course_id=mInstitutionCourse.id
               LEFT  JOIN ais_sale_center.m_agent mAgent ON mAgent.id=mStudentOfferItem.fk_agent_id

               LEFT JOIN (
                   SELECT offerItem.id,offerItem.fk_student_id ,offerItem.fk_area_country_id,offerItem.fk_institution_id,offerItem.fk_institution_course_id
                   FROM ais_sale_center.m_student_offer_item AS offerItem
                            INNER JOIN  ais_sale_center.r_student_offer_item_step rStudentOfferItemStep ON offerItem.id=rStudentOfferItemStep.fk_student_offer_item_id
                   WHERE    rStudentOfferItemStep.fk_student_offer_item_step_id IN(6,7,8,10)
                   GROUP BY offerItem.id,offerItem.fk_student_id,offerItem.fk_area_country_id,offerItem.fk_institution_id,offerItem.fk_institution_course_id
               ) b ON mStudent.id=b.fk_student_id

               LEFT  JOIN ais_institution_center.u_area_country uAreaCountry1 ON b.fk_area_country_id = uAreaCountry1.id
               LEFT  JOIN ais_institution_center.m_institution mInstitution1 ON b.fk_institution_id = mInstitution1.id
               LEFT  JOIN ais_institution_center.m_institution_course mInstitutionCourse1 ON b.fk_institution_course_id=mInstitutionCourse1.id





               LEFT  JOIN
                        (SELECT fk_student_offer_item_id,
                                MIN(fk_student_offer_item_step_id) fkStudentOfferItemStepIdmin,
                                MAX(fk_student_offer_item_step_id) fkStudentOfferItemStepIdmax FROM  ais_sale_center.r_student_offer_item_step
                            GROUP BY
                              fk_student_offer_item_id  ) a ON mStudentOfferItem.id=a.fk_student_offer_item_id
               LEFT  JOIN ais_sale_center.u_student_offer_item_step uStudentOfferItemStep ON a.fkStudentOfferItemStepIdmin=uStudentOfferItemStep.id
               LEFT  JOIN ais_sale_center.u_student_offer_item_step uStudentOfferItemStep2 ON a.fkStudentOfferItemStepIdmax=uStudentOfferItemStep2.id
        WHERE  1=1
        <!-- 基础权限控制条件 -->
        AND mStudentOfferItem.status=1
        AND mStudentOfferItem.is_follow=0
        AND mStudentOfferItem.is_follow_hidden=0
        AND mStudentOfferItem.fk_agent_id=#{agentId}

        <if test="studentName!=null and studentName!= '' ">

            AND
            (
            mStudent.name   like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(IFNULL(mStudent.last_name,''),IFNULL(mStudent.first_name,'')),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(IFNULL(mStudent.first_name,''),IFNULL(mStudent.last_name,'')),' ','') like concat('%',#{studentName},'%')
            )
        </if>

        <if test="gmtCreateStart!=null  and gmtCreateEnd!=null  ">
            AND mStudent.gmt_create BETWEEN #{gmtCreateStart} AND #{gmtCreateEnd}
        </if>


               LIMIT 1000;
    </select>


</mapper>