package com.partner.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.common.core.util.R;
import com.common.log.annotation.SysLog;
import com.common.security.annotation.Inner;
import com.partner.dto.student.MStudentParamsDto;
import com.partner.service.MStudentService;
import com.partner.vo.student.MStudentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 学生管理优化版本控制器
 * 
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/student/optimized")
@Tag(description = "学生管理优化版本", name = "学生管理优化版本")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Slf4j
public class MStudentOptimizedController {

    private final MStudentService mStudentService;

    /**
     * 优化版本：分页查询学生列表
     * 
     * 优化说明：
     * 1. 符合阿里规范：每次查询≤3表JOIN
     * 2. 数据库分页替代内存分页
     * 3. 批量查询消除N+1问题
     * 4. 分层查询降低复杂度
     */
    @Operation(summary = "优化版本-分页查询学生列表", description = "使用分层查询和批量查询优化性能")
    @GetMapping("/people/page")
    @SysLog("优化版本-分页查询学生列表")
    public R<IPage<MStudentVo>> getPeopleStudentListOptimized(
            @ParameterObject Page<MStudentVo> page,
            @ParameterObject @Valid MStudentParamsDto dto) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            IPage<MStudentVo> result = mStudentService.getPeopleStudentListOptimized(page, dto);
            
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            log.info("优化版本查询完成 - 页码: {}, 页大小: {}, 总记录数: {}, 执行时间: {}ms", 
                    page.getCurrent(), page.getSize(), result.getTotal(), executionTime);
            
            return R.ok(result);
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            log.error("优化版本查询失败 - 执行时间: {}ms, 错误信息: {}", executionTime, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 性能对比接口：原版本 vs 优化版本
     */
    @Operation(summary = "性能对比", description = "对比原版本和优化版本的性能差异")
    @GetMapping("/performance/compare")
    @SysLog("性能对比测试")
    public R<Object> performanceCompare(
            @ParameterObject Page<MStudentVo> page,
            @ParameterObject @Valid MStudentParamsDto dto) {
        
        // 测试原版本
        long originalStartTime = System.currentTimeMillis();
        IPage originalResult = mStudentService.getPeopleStudentList(page, dto);
        long originalEndTime = System.currentTimeMillis();
        long originalExecutionTime = originalEndTime - originalStartTime;
        
        // 测试优化版本
        long optimizedStartTime = System.currentTimeMillis();
        IPage<MStudentVo> optimizedResult = mStudentService.getPeopleStudentListOptimized(page, dto);
        long optimizedEndTime = System.currentTimeMillis();
        long optimizedExecutionTime = optimizedEndTime - optimizedStartTime;
        
        // 计算性能提升
        double performanceImprovement = originalExecutionTime > 0 ? 
                ((double)(originalExecutionTime - optimizedExecutionTime) / originalExecutionTime) * 100 : 0;
        
        // 构建对比结果
        Object compareResult = new Object() {
            public final String description = "性能对比结果";
            public final Object original = new Object() {
                public final long executionTime = originalExecutionTime;
                public final long totalRecords = originalResult.getTotal();
                public final String version = "原版本";
            };
            public final Object optimized = new Object() {
                public final long executionTime = optimizedExecutionTime;
                public final long totalRecords = optimizedResult.getTotal();
                public final String version = "优化版本";
            };
            public final Object performance = new Object() {
                public final double improvementPercentage = performanceImprovement;
                public final long timeSaved = originalExecutionTime - optimizedExecutionTime;
                public final String conclusion = performanceImprovement > 0 ? 
                        String.format("优化版本性能提升 %.2f%%", performanceImprovement) :
                        "优化版本性能无明显提升";
            };
        };
        
        log.info("性能对比完成 - 原版本: {}ms, 优化版本: {}ms, 性能提升: {:.2f}%", 
                originalExecutionTime, optimizedExecutionTime, performanceImprovement);
        
        return R.ok(compareResult);
    }

    /**
     * 内部接口：供其他服务调用的优化版本
     */
    @Operation(summary = "内部接口-优化版本学生列表", description = "供内部服务调用的优化接口")
    @PostMapping("/people/internal")
    @Inner
    public R<IPage<MStudentVo>> getPeopleStudentListInternal(
            @RequestBody Object requestData) {
        
        // 这里可以根据内部调用的需求进行参数解析
        // 暂时使用默认参数进行演示
        Page<MStudentVo> page = new Page<>(1, 10);
        MStudentParamsDto dto = new MStudentParamsDto();
        dto.setSearchType(0); // 按人查询
        
        IPage<MStudentVo> result = mStudentService.getPeopleStudentListOptimized(page, dto);
        
        return R.ok(result);
    }
}
