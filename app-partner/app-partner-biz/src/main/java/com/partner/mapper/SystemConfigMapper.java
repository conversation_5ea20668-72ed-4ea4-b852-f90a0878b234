package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.SystemConfigEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【system_config】的数据库操作Mapper
* @createDate 2025-01-23 17:22:07
* @Entity com.partner.entity.SystemConfig
*/
@Mapper
@DS("systemdb")
public interface SystemConfigMapper extends BaseMapper<SystemConfigEntity> {

}




