package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.partner.entity.PartnerRole;
import com.partner.entity.PartnerUserPartnerRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PartnerUserPartnerRoleMapper extends BaseMapper<PartnerUserPartnerRole> {

    /**
     * 根据伙伴用户Id查询角色列表
     * @param partnerUserId
     * @return
     */
    List<PartnerRole> selectRoleByPartnerUserId(@Param("partnerUserId") Long partnerUserId);


    /**
     * 根据伙伴用户Id查询权限列表
     * @param partnerUserId
     * @return
     */
    List<String> selectMenuPermissionKeyByPartnerUserId(@Param("partnerUserId") Long partnerUserId);
}
