package com.partner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.CommissionParamsDetailDto;
import com.partner.dto.CommissionParamsDto;
import com.partner.vo.CommissionDetailInfo;
import com.partner.vo.CommissionDetailVo;
import com.partner.vo.combox.GroupResultCombox;
import com.partner.vo.combox.CountryCombox;
import com.partner.vo.combox.LeaveResultCombox;
import com.partner.vo.combox.TypeResultCombox;
import com.pmp.vo.institution.InstitutionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionCommissionMapper extends BaseMapper<CommissionDetailVo> {

    IPage<CommissionDetailVo> getCommissionPage(Page page, @Param("query") CommissionParamsDto dto);


    List<CountryCombox> getCountryCombox(CommissionParamsDetailDto dto);

    List<CountryCombox> getCountryComboxAll(CommissionParamsDetailDto dto);


    List<TypeResultCombox> getTypeList(CommissionParamsDetailDto dto);


    List<GroupResultCombox> getGroupByCommission(CommissionParamsDetailDto dto);



    List<LeaveResultCombox> getLeaveList(CommissionParamsDetailDto dto);



    List<CommissionDetailVo> getTallCommissionList(CommissionParamsDto dto);
    List<CommissionDetailInfo> getCommissionDetail(CommissionParamsDetailDto dto);

    /**
     * 获取学校封面
     * @param institutionIds
     * @return
     */
    List<InstitutionVo> selectInstitutionCover(List<Long> institutionIds);

}
