package com.partner.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.partner.dto.base.UserInfoParams;
import com.partner.dto.student.paramsmapper.MStudentParams;
import com.partner.mapper.StudentPermissionMapper;
import com.partner.service.StudentPermissionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 学生权限控制服务实现
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Service
@AllArgsConstructor
public class StudentPermissionServiceImpl implements StudentPermissionService {
    
    private final StudentPermissionMapper studentPermissionMapper;
    
    @Override
    public Set<Long> getAccessibleStudentIds(UserInfoParams userInfo, MStudentParams params) {
        Set<Long> studentIds = new HashSet<>();
        
        // 检查权限
        if (!hasAnyStudentViewPermission(userInfo)) {
            log.warn("用户{}无学生查看权限", userInfo.getPartnerUserId());
            return studentIds;
        }
        
        // 检查代理商ID
        if (ObjectUtil.isEmpty(params.getAgentId())) {
            log.warn("代理商ID为空，无法查询学生");
            return studentIds;
        }
        
        if (hasStudentViewAllPermission(userInfo)) {
            // 全部权限：查询代理下所有学生ID
            log.info("用户{}拥有全部权限，查询代理{}下所有学生", userInfo.getPartnerUserId(), params.getAgentId());
            List<Long> allStudentIds = studentPermissionMapper.getAllStudentIdsByAgent(params.getAgentId());
            studentIds.addAll(allStudentIds);
            
        } else if (hasStudentViewPersonPermission(userInfo)) {
            // 个人权限：自己 + 下级的学生
            log.info("用户{}拥有个人权限，查询自己和下级的学生", userInfo.getPartnerUserId());
            
            List<Long> levelPartnerUserIds = userInfo.getLevelPartnerUserIds();
            if (CollectionUtils.isNotEmpty(levelPartnerUserIds)) {
                // 查询分配给用户的学生ID
                List<Long> assignedStudentIds = studentPermissionMapper.getAssignedStudentIds(levelPartnerUserIds);
                studentIds.addAll(assignedStudentIds);
                
                // 查询用户创建的学生ID
                List<Long> createdStudentIds = studentPermissionMapper.getCreatedStudentIds(levelPartnerUserIds);
                studentIds.addAll(createdStudentIds);
                
                log.info("用户{}及下级{}，分配学生{}个，创建学生{}个", 
                    userInfo.getPartnerUserId(), levelPartnerUserIds.size(), 
                    assignedStudentIds.size(), createdStudentIds.size());
            }
        }
        
        log.info("用户{}最终可访问学生{}个", userInfo.getPartnerUserId(), studentIds.size());
        return studentIds;
    }
    
    @Override
    public boolean hasStudentViewAllPermission(UserInfoParams userInfo) {
        if (CollectionUtils.isEmpty(userInfo.getPermissionKeys())) {
            return false;
        }
        return userInfo.getPermissionKeys().contains("STUDENT_VIEW_ALL");
    }
    
    @Override
    public boolean hasStudentViewPersonPermission(UserInfoParams userInfo) {
        if (CollectionUtils.isEmpty(userInfo.getPermissionKeys())) {
            return false;
        }
        return userInfo.getPermissionKeys().contains("STUDENT_VIEW_PERSON");
    }
    
    @Override
    public boolean hasAnyStudentViewPermission(UserInfoParams userInfo) {
        return hasStudentViewAllPermission(userInfo) || hasStudentViewPersonPermission(userInfo);
    }
}
