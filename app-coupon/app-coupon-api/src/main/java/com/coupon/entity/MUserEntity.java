package com.coupon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("m_coupon_user")
@Schema(description = "用户表")
public class MUserEntity extends Model<MUserEntity> {
    @TableId(type = IdType.AUTO) // 使用数据库自增主键
    @Schema(description = "用户Id")
    private Long id;

    @Schema(description = "姓名（中文）")
    private String name;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "性别：0女/1男")
    private Integer gender;

    @Schema(description = "移动电话")
    private String mobile;

    @Schema(description = "手机区号")
    private String mobileAreaCode;

    @Schema(description = "公司名称")
    private String company;

    @Schema(description = "大区id")
    private String fkAreaRegionIds;

    @Schema(description = "BD编号")
    private String bdCode;

    @Schema(description = "微信号")
    private String wechat;

    @Schema(description = "微信昵称")
    private String wechatNickname;

    @Schema(description = "微信头像URL")
    private String wechatIconUrl;

    @Schema(description = "角色")
    private String role;

    @Schema(description = "最后一次登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "删除标记，0未删除，1已删除")
    private Boolean isDelFlag;

    @Schema(description = "创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description = "修改时间")
    private LocalDateTime gmtModified;

    @Schema(description = "修改用户(登录账号)")
    private String gmtModifiedUser;

    @Schema(description = "系统用户id")
    private Long fkUserId;
}
