package com.coupon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@TableName("m_app_introduction")
@Schema(description = "小程序简介")
public class MAppIntroductionEntity {
    @TableId(type = IdType.AUTO)
    @Schema(description = "优惠卷Id")
    private Long id;

    @Schema(description = "小程序简介")
    private String introduction;

    @Schema(description = "是否激活：0否/1是")
    private Boolean isActive = true;

    @Schema(description = "创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description = "修改时间")
    private LocalDateTime gmtModified;

    @Schema(description = "修改用户(登录账号)")
    private String gmtModifiedUser;
}
