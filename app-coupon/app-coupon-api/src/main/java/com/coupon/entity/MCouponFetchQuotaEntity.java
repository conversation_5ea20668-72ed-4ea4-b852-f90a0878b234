package com.coupon.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("m_coupon_fetch_quota")
@Schema(description = "优惠卷配额")
public class MCouponFetchQuotaEntity {
    @TableId(type = IdType.AUTO) // 使用数据库自增主键
    @Schema(description = "优惠卷类型用户角色配额设置Id")
    private Long id;

    @Schema(description = "外键，优惠卷类型Id")
    private Long fkCouponTypeId;

    @Schema(description = "用户角色Code")
    private String fkRoleCode;

    @Schema(description = "配额")
    private Integer quota;

    @Schema(description = "创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description = "创建用户(登录账号)")
    private String gmtCreateUser;

    @Schema(description = "修改时间")
    private LocalDateTime gmtModified;

    @Schema(description = "修改用户(登录账号)")
    private String gmtModifiedUser;
}
