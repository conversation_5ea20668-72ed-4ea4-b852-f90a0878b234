package com.coupon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class EnrollUserDto {
    //姓名
    private String name;
    //昵称
    private String nickName;
    //性别
    private Integer gender;
    //手机号
    private String mobile;
    //微信号
    private String wechat;
    //身份
    private String role;
    //验证码
    private String captcha;
    //公司名称
    private String company;
    //手机区号
    private String mobileAreaCode;
    //BD
    private String bdCode;
    //大区
    private String fkAreaRegionIds;

    @Schema(description = "平台/应用Code")
    private String platformCode;

    @Schema(description = "平台/应用Id")
    private Long platformId;
}
