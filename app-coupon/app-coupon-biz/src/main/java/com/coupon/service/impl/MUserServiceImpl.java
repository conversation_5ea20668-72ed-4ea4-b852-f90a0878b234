package com.coupon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.common.core.util.R;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.coupon.dto.UserDto;
import com.coupon.entity.MUserEntity;
import com.coupon.mapper.MUserMapper;
import com.coupon.service.MUserService;
import com.coupon.vo.AreaRegionSelectVo;
import com.coupon.vo.BdVo;
import com.coupon.vo.UserInfoVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Service
@AllArgsConstructor
public class MUserServiceImpl extends ServiceImpl<MUserMapper, MUserEntity> implements MUserService {


    private final MUserMapper mUserMapper;



    @Override
    public List<AreaRegionSelectVo> getAreaRegionSelect(Long fkCompanyId) {
        return mUserMapper.getAreaRegionSelect(fkCompanyId);
    }

    @Override
    public Boolean userExists(UserDto userDto) {
        if(Objects.nonNull(userDto)){
            int num = mUserMapper.checkUserExists(userDto);
            if(num>0){
                return true;
            }
        }
        return false;
    }

    @Override
    public List<BdVo> getBDByAreaRegion(Long fkAreaRegionId) {
       List<BdVo>  list =mUserMapper.getBDByAreaRegion(fkAreaRegionId);
        for(BdVo bdVo:list){
            if(bdVo.getBdCode().equals("1707")){
                String originalStaffName = bdVo.getStaffName();
                if (originalStaffName != null && originalStaffName.matches("^(.*)\\（[^\\（]*\\）\\（[^\\（]*\\）$")) {
                    // 使用正则表达式匹配并替换
                    String newStaffName = originalStaffName.replaceAll("^(.*)\\（[^\\（]*\\）\\（([^\\（]*)\\）$", "$1（$2）");
                    bdVo.setStaffName(newStaffName);
                }
            }
        }
        return list;
    }

    @Override
    public R getDetail() {
        FzhUser fzhUser = SecurityUtils.getUser();
        MUserEntity mUserEntity = mUserMapper.selectOne(new LambdaQueryWrapper<MUserEntity>().eq(MUserEntity::getFkUserId, fzhUser.getId()));
        if (Objects.isNull(mUserEntity)) {
            return R.restResult(null, 40003, "用户不存在");
        }
        UserDto userDto = new UserDto();
        userDto.setId(mUserEntity.getId());
        UserInfoVo userInfoVo = mUserMapper.getDetail(userDto);
        if(Objects.isNull(userInfoVo)){
          return R.restResult(null, 40003, "用户不存在");
        }
        return R.ok(userInfoVo);
    }

    @Override
    public R updateUser(UserDto userDto) {
        if (userDto.getId() == null) {
            return R.restResult(null, 0, "用户id不能为空");
        }

        // 创建 LambdaUpdateWrapper 并设置条件
        LambdaUpdateWrapper<MUserEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MUserEntity::getId, userDto.getId());

        // 动态设置要更新的字段
        if (userDto.getNickname() != null && !userDto.getNickname().isEmpty()) {
            updateWrapper.set(MUserEntity::getNickname, userDto.getNickname());
        }

        if (userDto.getWechat() != null && !userDto.getWechat().isEmpty()) {
            updateWrapper.set(MUserEntity::getWechat, userDto.getWechat());
        }
        // 如果没有任何字段需要更新，则不执行更新操作
        if (updateWrapper.getSqlSet() == null || updateWrapper.getSqlSet().isEmpty()) {
            return R.ok(); // 或者抛出异常或返回提示信息
        }
        updateWrapper.set(MUserEntity::getGmtModifiedUser, SecurityUtils.getUser().getLoginId());
        updateWrapper.set(MUserEntity::getGmtModified,LocalDateTime.now());
        // 执行更新操作
        int num =mUserMapper.update(null, updateWrapper);
        if(num>0){
          UserInfoVo userInfoVo=  mUserMapper.getDetail(userDto);
            return  R.ok(userInfoVo);
        }
       return R.restResult(null, 0, "服务错误");
    }
}
