package com.apps.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Author:Oliver
 * @Date: 2025/1/18  14:26
 * @Version 1.0
 * 密码加密配置类
 */
@Configuration
@ConfigurationProperties(prefix = "encrypt")
@RefreshScope
@Data
public class EncryptConfig {
    @Value("${encrypt.key:thanks,fzh202411}")
    private String key;

    @Value("${encrypt.defaultPassword:123456}")
    private String defaultPassword;
}
