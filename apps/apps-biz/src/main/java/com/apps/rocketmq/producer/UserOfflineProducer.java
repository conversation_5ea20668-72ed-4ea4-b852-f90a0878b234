package com.apps.rocketmq.producer;

import com.alibaba.fastjson.JSONObject;
import com.apps.rocketmq.msg.UserOfflineDto;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author:Oliver
 * @Date: 2025/3/20
 * @Version 1.0
 * @apiNote:用户下线消息发送者
 */
@Slf4j
@Component
@Data
public class UserOfflineProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.producer.group:user_offline_topic}")
    private String topic;


    public void sendUserOfflineMessage(UserOfflineDto offlineDto) {
        log.info("发送用户下线消息，offlineDto={},topic={}", JSONObject.toJSONString(offlineDto), topic);
        rocketMQTemplate.asyncSend(topic, offlineDto, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("用户下线消息发送成功，offlineDto={}，消息ID={}", offlineDto, sendResult.getMsgId());
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("用户下线消息发送失败，offlineDto={}，异常信息={}", offlineDto, throwable.getMessage());
                // 这里可以做失败告警、日志记录等处理
            }
        });
    }
}
