package com.apps.service;

import com.apps.api.dto.system.SaveUserPlatformLoginDto;
import com.apps.api.dto.system.UpdateUserPasswordDto;
import com.apps.api.entity.SystemUserPlatformLoginEntity;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 */
public interface SystemUserPlatformLoginService extends IService<SystemUserPlatformLoginEntity> {

    /**
     * 根据平台id和code和openid获取用户
     * @param fkPlatformId
     * @param fkPlatformCode
     * @param openId
     * @return
     */
    SystemUserPlatformLoginEntity getUserByOpenId(Long fkPlatformId,String fkPlatformCode, String openId);

    /**
     * 保存用户平台登录信息
     * @param platformLoginDto
     * @return
     */
    Boolean saveUserPlatformLogin(SaveUserPlatformLoginDto platformLoginDto);

}
