package com.apps.service.strategy.impl;

import com.apps.api.dto.AppLoginDto;
import com.apps.api.vo.system.UserPermissionVo;
import com.apps.service.strategy.AppLoginVerifyStrategy;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Author:<PERSON>
 * @Date: 2025/1/9  19:11
 * @Version 1.0
 */
@Service("QRCODE")
@Slf4j
@AllArgsConstructor
public class QrCodeStrategyImpl implements AppLoginVerifyStrategy {

    @Override
    public UserPermissionVo appLoginVerify(AppLoginDto loginDto) {
        return null;
    }
}
