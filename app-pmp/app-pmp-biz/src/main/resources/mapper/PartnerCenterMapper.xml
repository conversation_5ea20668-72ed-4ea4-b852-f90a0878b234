<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pmp.mapper.PartnerCenterMapper">

    <resultMap id="PartnerUserResultMap" type="com.pmp.vo.partner.PartnerUserVo">
        <id property="partnerUserId" column="partnerUserId"/>
        <result property="userId" column="userId"/>
        <result property="agentId" column="agentId"/>
        <result property="companyId" column="companyId"/>
        <!-- 需要转换 countryIds 字符串为 List<Long> -->
        <result property="countryIds" column="countryIds" javaType="java.util.List"
                typeHandler="com.pmp.config.LongListTypeHandler"/>
    </resultMap>

    <select id="getPartnerUser" resultMap="PartnerUserResultMap">
        SELECT pu.id                               AS partnerUserId,
               pu.fk_user_id                       AS userId,
               pu.fk_agent_id                      AS agentId,
               pu.fk_company_id                    AS companyId,
               GROUP_CONCAT(ra.fk_area_country_id) AS countryIds
        FROM m_partner_user pu
                 LEFT JOIN
             r_partner_user_area_country ra ON pu.id = ra.fk_partner_user_id
        WHERE pu.fk_user_id = #{userId}
        GROUP BY pu.id, pu.fk_user_id, pu.fk_agent_id,pu.fk_company_id
        order by pu.id desc
        limit 1
    </select>

    <select id="getCompanyIdByAccount" resultType="java.lang.Long">
        SELECT pu.fk_company_id
        FROM m_partner_user pu
        WHERE pu.fk_user_login_id = #{account} LIMIT 1
    </select>

    <select id="getSystemUserByAccount" resultType="com.pmp.vo.partner.SystemUserVo">
        SELECT u.id AS userId,
               u.is_lock_flag,
               u.is_del_flag
        FROM app_system_center.system_user u
        WHERE exists(SELECT 1
                     FROM app_system_center.system_user_platform_login l
                     WHERE l.fk_user_id = u.id
                       AND l.login_id = #{account}
                       and l.fk_platform_code = 'PARTNER')
        LIMIT 1
    </select>

</mapper>