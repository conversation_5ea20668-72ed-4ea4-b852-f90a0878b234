server:
  port: 6013

spring:
  application:
    name: app-pmp-biz
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: 192.168.2.31:${NACOS_PORT:8848}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml

#配置日志
logging:
  config: classpath:log/<EMAIL>@.xml


