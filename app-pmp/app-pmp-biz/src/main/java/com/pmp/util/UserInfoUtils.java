package com.pmp.util;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.apps.api.enums.GlobExceptionEnum;
import com.common.security.service.FzhUser;
import com.common.security.util.SecurityUtils;
import com.pmp.exception.PmpGlobalException;
import com.pmp.mapper.PartnerCenterMapper;
import com.pmp.vo.partner.PartnerUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * @Author:Oliver
 * @Date: 2025/3/4  10:02
 * @Version 1.0
 */

@Slf4j
@Component
public class UserInfoUtils {

    private final PartnerCenterMapper partnerCenterMapper;

    @Autowired
    public UserInfoUtils(PartnerCenterMapper partnerCenterMapper) {
        this.partnerCenterMapper = partnerCenterMapper;
    }

    private static final Set<String> IGNORE_ROLE_CODES = new HashSet<String>() {{
        add("ADMIN");
        add("FINANCE");
        add("DIRECTOR");
    }};

    public PartnerUserVo getPartnerUser() {
        FzhUser user = SecurityUtils.getUser();
        if (Objects.isNull(user)){
            return null;
        }
        PartnerUserVo partnerUser = partnerCenterMapper.getPartnerUser(user.getId());
        if (Objects.isNull(partnerUser)) {
            log.error("伙伴用户信息不存在，系统用户Id:{}", user.getId());
            throw new PmpGlobalException(GlobExceptionEnum.SYS_USER_NOT_EXISTS);
        }
        if (CollectionUtils.isEmpty(partnerUser.getCountryIds())) {
            partnerUser.setCountryIds(Arrays.asList(0L));
        }
        partnerUser.setFilterCountry(Boolean.TRUE);
        if (IGNORE_ROLE_CODES.contains(user.getRoleCode())) {
            partnerUser.setFilterCountry(Boolean.FALSE);
        }
        return partnerUser;
    }
}
