package com.pmp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pmp.entity.AgentCommissionPlan;
import com.pmp.vo.commission.TerritoryInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AgentCommissionPlanMapper extends BaseMapper<AgentCommissionPlan> {

    /**
     * 根据条件查询计划id
     *
     * @param agentId
     * @param companyId
     * @param startDate
     * @param type      1-查询通用 2-查询指定类型
     * @return
     */
    List<Long> selectPlanIds(@Param("agentId") Long agentId,
                             @Param("companyId") Long companyId,
                             @Param("startDate") Date startDate,
                             @Param("type") Integer type,
                             @Param("providerIds") List<Long> providerIds);

    /**
     * 获取学校佣金方案详情
     *
     * @param planId
     * @return
     */
    TerritoryInfoVo getTerritoryInfoVo(@Param("planId") Long planId);


    /**
     * 获取当前用户学校提供商IDS
     *
     * @param companyId
     * @return
     */
    List<Long> getCurrentUserProviderIds(@Param("providerIds") List<Long> providerIds, @Param("companyId") Long companyId);
}
