package com.pmp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pmp.entity.MajorLevelCustom;
import com.pmp.vo.commission.MajorLevelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MajorLevelCustomMapper extends BaseMapper<MajorLevelCustom> {

    /**
     * 查询课程等级列表
     *
     * @param isGeneral
     * @return
     */
    List<MajorLevelVo> selectMajorLevel(@Param("isGeneral") Integer isGeneral);
}
