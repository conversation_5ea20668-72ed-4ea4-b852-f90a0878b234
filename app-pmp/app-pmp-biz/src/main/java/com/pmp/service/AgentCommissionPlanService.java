package com.pmp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pmp.dto.CommissionDto;
import com.pmp.dto.DateDto;
import com.pmp.entity.AgentCommissionPlan;
import com.pmp.vo.commission.AgentCommissionDetailVo;
import com.pmp.vo.commission.AgentCommissionPlanVo;
import com.pmp.vo.commission.CommissionDetail;
import com.pmp.vo.commission.MergeCommissionVo;

import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface AgentCommissionPlanService extends IService<AgentCommissionPlan> {

    /**
     * 根据时间范围获取计划id列表
     *
     * @param startDate
     * @return
     */
    List<Long> getPlanIds(Long agentId,Long companyId,Date startDate);

    /**
     * 获取计划id列表-代理ID
     * @param startDate
     * @return
     */
    List<Long> getPlanIdsByAgentId(Long agentId,Long companyId,Date startDate);

    /**
     * 根据学校查询佣金方案
     *
     * @param dto
     * @return
     */
    List<AgentCommissionPlanVo> commissionPlanList(CommissionDto dto);

    /**
     * 根据学校id查询佣金方案详情
     *
     * @param dto
     * @return
     */
    AgentCommissionDetailVo commissionDetail(CommissionDto dto);


    /**
     * 获取学校佣金方案详情-多方案
     * @param dto
     * @return
     */
    List<CommissionDetail> getCommissionDetail(CommissionDto dto,Long agentId, Long companyId);


    /**
     * 学校佣金方案-组合和bonus合并,明细相同合并
     * @param dto
     * @return
     */
    MergeCommissionVo getMergeCommission(CommissionDto dto,Long agentId, Long companyId,String highCommissionCode);

    /**
     * 高佣学校数量
     * @param startDate
     * @return
     */
    Integer highCommissionCount(DateDto startDate);
}
