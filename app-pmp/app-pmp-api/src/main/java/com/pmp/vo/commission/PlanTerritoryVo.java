package com.pmp.vo.commission;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.pmp.enums.TerritoryRuleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/3/13
 * @Version 1.0
 * @apiNote:佣金方案适用地区
 */
@Data
public class PlanTerritoryVo {

    @Schema(description = "规则描述-英文")
    private String description;

    @Schema(description = "国家名称(逗号隔开)-英文")
    private String territories;

    @Schema(description = "规则")
    private Integer isInclude;

    @Schema(description = "国家ID集合")
    private List<Long> territoryIds;

    @Schema(description = "规则描述-中文")
    private String descriptionChn;

    @Schema(description = "国家名称(逗号隔开)-中文")
    private String territoriesChn;

    public static PlanTerritoryVo createGlobalTerritory() {
        PlanTerritoryVo planTerritoryVo = new PlanTerritoryVo();
        planTerritoryVo.setDescription(Objects.nonNull(TerritoryRuleEnum.getEnumByCode(5)) ? TerritoryRuleEnum.getEnumByCode(5).getMsg() : StringUtils.EMPTY);
        planTerritoryVo.setTerritories(StringUtils.EMPTY);
        planTerritoryVo.setDescriptionChn(Objects.nonNull(TerritoryRuleEnum.getEnumByCode(5)) ? TerritoryRuleEnum.getEnumByCode(5).getMsgChn() : StringUtils.EMPTY);
        planTerritoryVo.setTerritoriesChn(StringUtils.EMPTY);
        return planTerritoryVo;
    }
}
