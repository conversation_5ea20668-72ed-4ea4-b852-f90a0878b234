package com.pmp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("m_agent_commission_plan")
public class AgentCommissionPlan extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @Schema(description = "代理佣金分类Id")
    private Long fkAgentCommissionTypeId;

    @Schema(description = "学校提供商佣金方案Id")
    private Long fkInstitutionProviderCommissionPlanId;

    @Schema(description = "方案名称")
    private String name;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "方案有效时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "方案有效时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @Schema(description = "是否全局模板：0否/1是")
    private Integer isGobal;

    @Schema(description = "是否激活：0否/1是")
    private Integer isActive;

    @Schema(description = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

}
