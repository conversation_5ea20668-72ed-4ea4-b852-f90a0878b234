package com.payment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付类型枚举
 */
@Getter
@AllArgsConstructor
public enum PayStatusEnum {

    UN_PAID(0, "未支付"),
    PAY_SUCCESS(1, "支付成功"),
    PAY_FAIL(-1, "支付成功失败"),
    ;


    private Integer code;

    private String msg;


    public static PayStatusEnum getEnumByCode(Integer code) {
        for (PayStatusEnum value : PayStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
