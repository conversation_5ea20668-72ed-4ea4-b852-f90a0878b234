package com.payment.feign;

import com.common.core.util.R;
import com.common.feign.annotation.NoToken;
import com.common.mybatis.tenant.FzhFeignTenantInterceptor;
import com.payment.dto.common.PayRequest;
import com.payment.vo.PayResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author:Oliver
 * @Date: 2025/1/15  16:44
 * @Version 1.0
 */
@FeignClient(contextId = "remotePmpService", value = "app-payment-biz", configuration = FzhFeignTenantInterceptor.class)
public interface RemotePaymentService {

    /**
     * 下单
     *
     * @param payRequest
     * @return
     */
    @NoToken
    @PostMapping("/feign/order/unifiedOrder")
    R<PayResponse> unifiedOrder(@RequestBody PayRequest payRequest);

}
