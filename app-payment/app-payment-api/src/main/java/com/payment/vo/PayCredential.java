package com.payment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/7/4
 * @Version 1.0
 * @apiNote:支付信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayCredential {

    @Schema(description = "appId")
    private String appId;

    @Schema(description = "时间戳")
    private String timeStamp;

    @Schema(description = "随机字符串")
    private String nonceStr;

    @Schema(description = "预支付交易会话ID")
    private String prepayId;

    @Schema(description = "签名方式")
    private String signType;

    @Schema(description = "签名")
    private String paySign;

    @Schema(description = "支付宝支付（直接返回 orderStr）-支付宝支付")
    private String alipayOrderStr;

    @Schema(description = "二维码地址-扫码支付")
    private String qrCodeUrl;

    @Schema(description = "H5地址-H5支付")
    private String h5Url;
}
