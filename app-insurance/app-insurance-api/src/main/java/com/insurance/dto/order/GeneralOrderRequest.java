package com.insurance.dto.order;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/5/16
 * @Version 1.0
 * @apiNote:通用下单请求类
 */
@Data
public class GeneralOrderRequest {

    @Schema(description = "保险产品类型Key")
    @NotBlank(message = "保险产品类型Key不能为空")
    private String productTypeKey;

    @Schema(description = "加密密钥")
//    @NotBlank(message = "加密密钥不能为空")
    private String secretKey;

    @Schema(description = "加密卡号")
//    @NotBlank(message = "加密卡号不能为空")
    private String encryptedCardNumber;

    @Schema(description = "卡号后三位")
//    @NotBlank(message = "卡号后三位不能为空")
    private String cvc;

    @Schema(description = "表单信息")
    @NotNull(message = "表单信息不能为空")
    private JSONObject formData;

    @Schema(description = "保险单类型")
    @NotBlank(message = "保险单类型不能为空")
    private String insuranceType;



    @Schema(description = "受保人姓名")
    @NotBlank(message = "保受保人姓名不能为空")
    private String insurantName;

    @Schema(description = "受保人姓（英/拼音）")
    private String insurantLastName;

    @Schema(description = "受保人名（英/拼音）")
    private String insurantFirstName;

    @Schema(description = "受保人护照号")
    private String insurantPassportNum;

    @Schema(description = "保单开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartTime;

    @Schema(description = "保单结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "Email")
    @Email(message = "邮箱格式不正确")
    @NotBlank(message = "Email不能为空")
    private String email;

    @Schema(description = "移动电话")
    private String mobile;

    @Schema(description = "保单备注（用户下单时填写）")
    private String orderRemark;

    @Schema(description = "受保人性别")
    private String insurantGender;

    @Schema(description = "受保人国籍")
    private String insurantNationality;

    @Schema(description = "入学时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date enrollmentTime;

    @Schema(description = "毕业时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date graduationTime;

    @Schema(description = "生日")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insurantBirthday;

    @Schema(description = "支付方式-1：信用卡支付;2:微信支付-默认是信用卡支付")
    private Integer payType = 1;

    @Schema(description = "金额-针对微信支付")
    private BigDecimal amount;

    @Schema(description = "用户openId-针对微信支付")
    private String openId;

}
