package com.insurance.dto.order;

import lombok.Data;

import java.util.List;

/**
 * @Author:<PERSON>
 * @Date: 2025/5/12
 * @Version 1.0
 * @apiNote:NIB登录下单表单数据
 */
@Data
public class NibLoginFormData {

    private Step1 step1;
    private Step2 step2;
    private Step3 step3;
    private Step4 step4;
    private String orderNo;

    @Data
    public static class Step1 {
        private String start_time;
        private String end_time;
        private String cover_for;
    }

    @Data
    public static class Step2 {
        private String title;
        private Integer primaryApplicant;
        private String given_name;
        private String family_name;
        private String sex;
        private String birth_day;
        private String birth_month;
        private String birth_year;
        private String email;
        private String phoneNumber;
        private String educational_institution;
        private Integer have_postal_address;
        private String postal_address;
        private String citizenship;
        private String passport_number;
        private Integer currently_in_australia;
        private Integer have_OSHC_health_insurance;
        private String last_OSHC_health_insurer;
        private String policy_number;
        private Integer readTermsAndConditions;
        private Integer collectingPII;
        private Integer verifyVisa;
    }

    @Data
    public static class Step3 {
        private Boolean add_cover;
        private String title;
        private String given_name;
        private String family_name;
        private String sex;
        private String birth_day;
        private String birth_month;
        private String birth_year;
        private List<DependantDetail> dependant_details;

        @Data
        public static class DependantDetail {
            private String given_name;
            private String family_name;
            private String sex;
            private String birth_day;
            private String birth_month;
            private String birth_year;
        }
    }

    @Data
    public static class Step4 {
        private String cardholder_name;
        private String card_number;
        private String expiry_date;
        private String cvv;
    }
}
