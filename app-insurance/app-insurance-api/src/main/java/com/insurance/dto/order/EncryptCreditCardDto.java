package com.insurance.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author:Oliver
 * @Date: 2025/7/3
 * @Version 1.0
 * @apiNote: 加密卡号信息请求体
 */
@Data
public class EncryptCreditCardDto {

    @Schema(description = "卡号")
    @NotBlank(message = "卡号不能为空")
    private String cardNumber;

    @Schema(description = "卡号后三位")
    @NotBlank(message = "卡号后三位不能为空")
    private String cvc;

}
