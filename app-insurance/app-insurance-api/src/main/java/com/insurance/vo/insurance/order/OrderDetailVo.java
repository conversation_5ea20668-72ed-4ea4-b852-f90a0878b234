package com.insurance.vo.insurance.order;

import com.insurance.entity.InsuranceOrder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:订单详情
 */
@Data
public class OrderDetailVo extends InsuranceOrder {

    @Schema(description = "保险公司名称")
    private String insuranceCompanyName;

    @Schema(description = "保险名称")
    private String productTypeName;
}
