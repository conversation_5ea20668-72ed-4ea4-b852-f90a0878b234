package com.insurance.vo.insurance.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/5/20
 * @Version 1.0
 * @apiNote:订单统计VO
 */
@Data
public class OrderStatisticsVo {

    @Schema(description = "订单总数")
    private Integer totalOrder;

    @Schema(description = "下单中总数")
    private Integer processOrder;

    @Schema(description = "已完成订单总数")
    private Integer completeOrder;

    @Schema(description = "下单失败总数")
    private Integer failOrder;

    public OrderStatisticsVo() {
        this.totalOrder = 0;
        this.processOrder = 0;
        this.completeOrder = 0;
        this.failOrder = 0;
    }
}

