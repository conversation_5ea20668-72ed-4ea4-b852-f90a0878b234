package com.insurance.vo.settlement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote:结算单明细vo
 */
@Data
public class SettlementBillItemVo extends SettlementOrderVo {

    @Schema(description = "保险公司名称")
    private String insuranceCompanyName;

    @Schema(description = "保险产品名称")
    private String productTypeName;

    @Schema(description = "结算金额")
    private BigDecimal settlementAmount;

    @Schema(description = "对账单明细ID")
    private Long settlementBillItemId;

    @Schema(description = "结算比例-带单位")
    private String commissionRateStr;
}
