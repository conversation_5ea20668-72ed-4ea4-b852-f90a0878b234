package com.insurance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 保险单类型举类
 */

@Getter
@AllArgsConstructor
public enum InsuranceTypeEnum {

    SINGLE("Single", "单人" ),
    COUPLE("Couple", "夫妻" ),
    FAMILY("Family", "家庭" ),
    ;

    private String code;

    private String msg;


    public static InsuranceTypeEnum getEnumByCode(String code) {
        for (InsuranceTypeEnum value : InsuranceTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
