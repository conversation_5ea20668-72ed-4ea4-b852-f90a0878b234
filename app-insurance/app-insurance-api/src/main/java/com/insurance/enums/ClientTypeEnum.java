package com.insurance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 客户类型枚举类
 */

@Getter
@AllArgsConstructor
public enum ClientTypeEnum {

    STUDENT(1, "学生" ),
    TOURIST(2, "游客" ),
    WORKER(3, "工作者" ),
    ;


    private Integer code;

    private String msg;


    public static ClientTypeEnum getEnumByCode(Integer code) {
        for (ClientTypeEnum value : ClientTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
