
package com.insurance.controller;


import com.alibaba.fastjson.JSONObject;
import com.apps.api.feign.RemoteAppsService;
import com.common.core.util.R;
import com.common.security.annotation.Inner;
import com.insurance.config.WxConfig;
import com.insurance.dto.order.EncryptCreditCardDto;
import com.insurance.dto.order.GeneralOrderRequest;
import com.insurance.entity.InsuranceOrder;
import com.insurance.exception.InsuranceGlobalException;
import com.insurance.service.InsuranceOrderService;
import com.insurance.util.InsuranceDateUtil;
import com.insurance.vo.insurance.order.EncryptCreditCardVo;
import com.insurance.vo.insurance.order.OrderDetailVo;
import com.insurance.vo.insurance.order.OrderStatisticsVo;
import com.payment.vo.PayResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("/order")
@Tag(description = "订单管理", name = "订单管理")
public class OrderController {

    @Autowired
    private InsuranceOrderService orderService;
    @Autowired
    private RemoteAppsService appsService;
    @Autowired
    private WxConfig wxConfig;

    @Operation(summary = "提交订单")
    @PostMapping("/submitOrder")
    public R submitOrder(@RequestBody @Valid GeneralOrderRequest request) {
        log.info("提交订单：{}", JSONObject.toJSONString(request));
        orderService.submitOrder(request);
        return R.ok("提交成功");
    }

    @Operation(summary = "加密卡号信息")
    @PostMapping("/encryptCreditCard")
    public R<EncryptCreditCardVo> encryptCreditCard(@RequestBody @Valid EncryptCreditCardDto creditCardDto) {
        return R.ok(orderService.encryptCreditCard(creditCardDto));
    }

    @Operation(summary = "发送下单请求")
    @GetMapping("/sendRequest")
    @Inner(false)
    public R sendRequest() {
        log.info("收到发送下单请求=====>");
        orderService.sendRequest();
        return R.ok();
    }

    @Operation(summary = "订单列表")
    @GetMapping("/getOrderList")
    public R<List<InsuranceOrder>> getOrderList(@Parameter(description = "已完成:2;下单中:1;下单失败:-2") Integer orderStatus,
                                                @Parameter(description = "时间范围(年月)：yyyy-MM") String date) {
        return R.ok(orderService.getOrderList(orderStatus, date));
    }

    @Operation(summary = "订单统计")
    @GetMapping("/getOrderStatistics")
    public R<OrderStatisticsVo> getOrderStatistics(@Parameter(description = "时间范围(年月)：yyyy-MM") String date) {
        return R.ok(orderService.getOrderStatistics(date));
    }

    @Operation(summary = "订单详情")
    @GetMapping("/getOrderDetail/{id}")
    public R<OrderDetailVo> getOrderDetail(@PathVariable("id") Long id) {
        return R.ok(orderService.getOrderDetail(id));
    }

    @Operation(summary = "根据课程开始结束日期计算保单生效日期")
    @GetMapping("/calculateInsuranceRange")
    public R<InsuranceDateUtil.InsuranceDateRange> calculateInsuranceRange(@Parameter(description = "时间范围(年月)：yyyy-MM") String startStr,
                                                                           @Parameter(description = "时间范围(年月)：yyyy-MM") String endStr) throws ParseException {
        return R.ok(InsuranceDateUtil.InsuranceDateRange.calculateInsuranceRange(startStr, endStr));
    }

    @Operation(summary = "获取用户openId")
    @GetMapping("/getOpenId")
    @Inner(false)
    public R getOpenId(String code) {
        R<String> result = appsService.getOpenId(code, wxConfig.getAppId(), wxConfig.getAppSecret());
        if (!result.isSuccess() || StringUtils.isBlank(result.getData())) {
            log.error("微信获取openId失败,返回结果:{}", JSONObject.toJSONString(result));
            log.error("微信获取openId失败,参数:code:{},appid:{},secret:{}", code, wxConfig.getAppId(), wxConfig.getAppSecret());
            throw new InsuranceGlobalException(500, "微信获取openId失败");
        }
        return R.ok(result.getData());
    }

    @Operation(summary = "提交微信订单")
    @PostMapping("/submitWxPayOrder")
    public R<PayResponse> submitWxPayOrder(@RequestBody @Valid GeneralOrderRequest request) {
        return R.ok(orderService.submitWxPayOrder(request));
    }

}
