package com.insurance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.insurance.entity.PartnerUserClient;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface PartnerUserClientMapper extends BaseMapper<PartnerUserClient> {

    /**
     * 根据伙伴用户Id和进度查询客户列表
     *
     * @param agentId
     * @param progress
     * @return
     */
    List<PartnerUserClient> getClientList(@Param("partnerUserId") Long partnerUserId,
                                          @Param("progress") Integer progress,
                                          @Param("keyword") String keyword);
}
