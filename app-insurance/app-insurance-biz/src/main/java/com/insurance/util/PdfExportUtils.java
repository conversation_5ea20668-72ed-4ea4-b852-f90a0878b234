package com.insurance.util;

import com.alibaba.nacos.common.utils.StringUtils;
import com.insurance.service.MediaAndAttachedService;
import com.insurance.vo.settlement.AgentAccountVo;
import com.insurance.vo.settlement.SettlementBillDetailVo;
import com.insurance.vo.settlement.SettlementBillItemVo;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class PdfExportUtils {

    @Autowired
    private MediaAndAttachedService mediaAndAttachedService;


    /**
     * 下载对账单-PDF
     *
     * @param billDetail
     * @param response
     * @throws IOException
     * @throws DocumentException
     */
    public void downloadPdf(SettlementBillDetailVo billDetail, HttpServletResponse response, Boolean upload) throws IOException, DocumentException {

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter.getInstance(document, baos);
        document.open();
        buildPdfContent(document, billDetail);
        document.close();

        // 1. 下载响应设置
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=settlement.pdf");
        response.setContentLength(baos.size());

        // 2. 返回 PDF
        ServletOutputStream out = response.getOutputStream();
        baos.writeTo(out);
        out.flush();

        //3. 构造 MultipartFile 并上传到文件桶
        if (upload) {
            // 获取当前日期字符串
            String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            // 拼接文件名
            String fileName = "settlement-" + dateStr + ".pdf";
            byte[] pdfBytes = baos.toByteArray();
            MultipartFile multipartFile = FileConvertUtils.toMultipartFile(
                    pdfBytes,
                    "file",
                    fileName,
                    "application/pdf");
            try {
                log.info("开始上传文件,对账单Id:{}", billDetail.getSettlementBillId());
                mediaAndAttachedService.uploadAttached(multipartFile, billDetail.getSettlementBillId());
                log.info("上传文件成功,对账单Id:{}", billDetail.getSettlementBillId());
            } catch (Exception e) {
                log.error("上传文件异常", e);
                log.error("上传文件异常,失败信息:{}", e.getMessage());
            }
        }
    }

    public static void buildPdfContent(Document document, SettlementBillDetailVo billDetail) {
        try {
            float availableWidth = PageSize.A4.getWidth() - document.leftMargin() - document.rightMargin();
            int col = 12;

            document.add(buildTitleImage(availableWidth, col));
            // 插入空段落作为分隔
            document.add(buildTitleRow(col, billDetail));
            document.add(buildMainTable(col, billDetail));
            document.add(new Paragraph(" "));

            document.add(buildSettlementSummaryTable(col, billDetail));
            document.add(new Paragraph(" "));

            document.add(buildBankInfoTable(col, billDetail));
            document.add(new Paragraph(" "));

            document.add(buildNoticeAndSignatureTable(col, billDetail));

        } catch (Exception e) {
            log.error("构建PDF内容异常", e);
        }
    }

    public static PdfPTable buildTitleImage(float availableWidth, int col) throws Exception {
        PdfPTable table = new PdfPTable(col);
        table.setWidthPercentage(100);

        float[] columnWidths = new float[col];
        Arrays.fill(columnWidths, 1f);
        table.setWidths(columnWidths);

        byte[] imgBytes = PdfUtils.getResourceAsBytes("template/title.png");
        Image image = Image.getInstance(imgBytes);

        // 不用 scaleAbsolute，保持图片等比例撑满宽度即可
        image.scaleToFit(availableWidth, 100); // 高度设置为你想要的最大值，例如100

        PdfPCell cell = new PdfPCell(image);
        cell.setColspan(col);
        cell.setBorder(PdfPCell.NO_BORDER);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);

        table.addCell(cell);
        return table;
    }


    public static PdfPTable buildTitleRow(int col, SettlementBillDetailVo billDetail) throws DocumentException {
        PdfPTable table = new PdfPTable(col);
        table.setWidthPercentage(100);

        float[] columnWidths = new float[col];
        Arrays.fill(columnWidths, 1f);
        table.setWidths(columnWidths);

        String text = "代理名称：" + Optional.ofNullable(billDetail.getAgentAccount())
                .map(AgentAccountVo::getAgentName).orElse("");

        // 这里确保 addTitleOrColCell 生成的单元格已经设置了 colspan=col
        PdfPCell cell = PdfUtils.addTitleOrColCell(col, text, null, 30, 16);
        table.addCell(cell);

        return table;
    }

    public static PdfPTable buildMainTable(int col, SettlementBillDetailVo billDetail) throws Exception {
        PdfPTable table = new PdfPTable(col);
        table.setWidths(new float[]{2.5f, 1.5f, 1.5f, 2.5f, 2.5f, 2.5f, 2.5f, 2.5f, 1.3f, 1.3f, 1.3f, 1.3f});
        table.setWidthPercentage(100);

        // Header
        List<String> headers = Arrays.asList("保单号", "保险公司", "保险产品", "学生姓名", "生日",
                "保险购买日期", "保险生效日期", "保险结束日期", "币种", "保险金额", "结算比例", "结算金额");
        headers.forEach(h -> table.addCell(PdfUtils.addTableHeadCell(h)));

        // Data
        for (SettlementBillItemVo item : billDetail.getBillItems()) {
            table.addCell(PdfUtils.addTitleOrColCell(1, item.getInsuranceNum(), null, 30, 8));
            table.addCell(PdfUtils.addTitleOrColCell(1, item.getInsuranceCompanyName(), null, 30, 8));
            table.addCell(PdfUtils.addTitleOrColCell(1, item.getProductTypeName(), null, 30, 8));
            table.addCell(PdfUtils.addTitleOrColCell(1, item.getInsurantName(), null, 30, 8));
            table.addCell(PdfUtils.addTitleOrColCell(1, PdfUtils.formatDate(item.getInsurantBirthday()), null, 30, 8));
            table.addCell(PdfUtils.addTitleOrColCell(1, PdfUtils.formatDate(item.getGmtCreate()), null, 30, 8));
            table.addCell(PdfUtils.addTitleOrColCell(1, PdfUtils.formatDate(item.getInsuranceStartTime()), null, 30, 8));
            table.addCell(PdfUtils.addTitleOrColCell(1, PdfUtils.formatDate(item.getInsuranceEndTime()), null, 30, 8));
            table.addCell(PdfUtils.addTitleOrColCell(1, item.getFkCurrencyTypeNum(), null, 30, 8));
            table.addCell(PdfUtils.addTitleOrColCell(1, item.getInsuranceAmount().toString(), null, 30, 8));
            table.addCell(PdfUtils.addTitleOrColCell(1, item.getCommissionRateStr(), null, 30, 8));
            table.addCell(PdfUtils.addTitleOrColCell(1, item.getSettlementAmount().toString(), null, 30, 8));
        }

        return table;
    }

    public static PdfPTable buildSettlementSummaryTable(int col, SettlementBillDetailVo billDetail) throws Exception {
        PdfPTable table = new PdfPTable(col);
        table.setWidths(new float[]{2.5f, 1.5f, 1.5f, 2.5f, 2.5f, 2.5f, 2.5f, 2.5f, 1.3f, 1.3f, 1.3f, 1.3f});
        table.setWidthPercentage(100);

        table.addCell(PdfUtils.addTitleOrColCell(col, "本期结算佣金", new BaseColor(216, 231, 244), 24, 16));
        table.addCell(PdfUtils.addTitleOrColCell(3, billDetail.getOrderCurrencyTypeNum(), null, 24, 8));
        String total = billDetail.getOriginTotalSettlementAmount() + "  (小计：" +
                billDetail.getTotalSettlementAmount() + " " + billDetail.getSettlementCurrencyTypeNum() + ")";
        log.info("total: {}", total);
        table.addCell(PdfUtils.addTitleOrColCell(9, total, null, 24, 8));

        return table;
    }

    private static PdfPTable buildBankInfoTable(int col, SettlementBillDetailVo billDetail) throws Exception {
        PdfPTable table = new PdfPTable(col);
        table.setWidths(new float[]{2.5f, 1.5f, 1.5f, 2.5f, 2.5f, 2.5f, 2.5f, 2.5f, 1.3f, 1.3f, 1.3f, 1.3f});
        table.setWidthPercentage(100);

        // 添加顶部提示信息（红色 + 黑色文字混排）
        table.addCell(PdfUtils.addPartialColorCell(col, 40, 8));

        AgentAccountVo account = billDetail.getAgentAccount();

        table.addCell(PdfUtils.addTitleOrColCell(3, "Bank：", new BaseColor(216, 231, 244), 24, 8));
        table.addCell(PdfUtils.addTitleOrColCell(9, account.getBankName(), null, 24, 8));

        table.addCell(PdfUtils.addTitleOrColCell(3, "Branch：", new BaseColor(216, 231, 244), 24, 8));
        table.addCell(PdfUtils.addTitleOrColCell(9, account.getBankBranchName(), null, 24, 8));

        table.addCell(PdfUtils.addTitleOrColCell(3, "Account name：", new BaseColor(216, 231, 244), 24, 8));
        table.addCell(PdfUtils.addTitleOrColCell(9, account.getBankAccount(), null, 24, 8));

        table.addCell(PdfUtils.addTitleOrColCell(3, "Account No：", new BaseColor(216, 231, 244), 24, 8));
        table.addCell(PdfUtils.addTitleOrColCell(9, account.getBankAccountNum(), null, 24, 8));

        table.addCell(PdfUtils.addTitleOrColCell(3, "Bank Address：", new BaseColor(216, 231, 244), 24, 8));
        table.addCell(PdfUtils.addTitleOrColCell(9, account.getBankAddress(), null, 24, 8));

        table.addCell(PdfUtils.addTitleOrColCell(3, "SWIFT CODE(外币账户填写)：", new BaseColor(216, 231, 244), 24, 8));
        table.addCell(PdfUtils.addTitleOrColCell(9, account.getBankCode(), null, 24, 8));

        table.addCell(PdfUtils.addTitleOrColCell(3, "结算金额总计：", new BaseColor(216, 231, 244), 24, 8));
        String str = billDetail.getTotalSettlementAmount().toString() + billDetail.getSettlementCurrencyTypeNum()
                + " （汇率浮动，以实际结算金额及汇率为准）";
        table.addCell(PdfUtils.addTitleOrColCell(9, str, null, 24, 8));

        return table;
    }


    public static PdfPTable buildNoticeAndSignatureTable(int col, SettlementBillDetailVo billDetail) throws Exception {
        PdfPTable table = new PdfPTable(col);
        table.setWidthPercentage(100);


        table.addCell(PdfUtils.addTitleOrColCell(col, "注:佣金结算前必须保证已生成完整版合作协议", new BaseColor(216, 231, 244), 30, 8));
        table.addCell(PdfUtils.addTitleOrColCell(col, "结算周期：双月中旬邮件核对确认，建议收到核对邮件后第一时间回复确认。收到回复确认邮件后的 10 个工作日完成汇款", null, 30, 8));
        table.addCell(PdfUtils.addTitleOrColCell(col, "人民币结算：支付渠道为易思汇，汇率按照划款当天香港中国银行十点的汇率，首先外币转成港币，在港币转成人民币，会涉及两个汇率。第三方每笔收取 100 人民币\n" +
                "手续费。且协议甲方必须和收款账户保持一致。", null, 30, 8));

        PdfPCell signLabel = PdfUtils.addTitleOrColCell(2, "签名：", null, 60, 16);
        signLabel.setBorder(PdfPCell.LEFT | PdfPCell.TOP | PdfPCell.BOTTOM);
        table.addCell(signLabel);

        if (StringUtils.isNotBlank(billDetail.getSignature())) {
            byte[] signImg = PdfUtils.convertBase64ToPng(billDetail.getSignature());
            Image image = Image.getInstance(signImg);
            image.scaleToFit(200f, 60f);

            PdfPCell imageCell = new PdfPCell(image);
            imageCell.setColspan(10);
            imageCell.setFixedHeight(80);
            imageCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            imageCell.setBorder(PdfPCell.RIGHT | PdfPCell.TOP | PdfPCell.BOTTOM);
            table.addCell(imageCell);
        } else {
            PdfPCell empty = new PdfPCell();
            empty.setColspan(10);
            empty.setFixedHeight(80);
            empty.setBorder(PdfPCell.RIGHT | PdfPCell.TOP | PdfPCell.BOTTOM);
            table.addCell(empty);
        }

        return table;
    }
}
