package com.insurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.insurance.dto.settlement.SubmitSettlementDto;
import com.insurance.entity.SettlementBill;
import com.insurance.vo.settlement.AgentAccountVo;
import com.insurance.vo.settlement.HistoricalBillVo;
import com.insurance.vo.settlement.SettlementBillDetailVo;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/6/13
 * @Version 1.0
 * @apiNote:
 */
public interface SettlementBillService extends IService<SettlementBill> {

    /**
     * 代理账户列表
     *
     * @return
     */
    Map<String, List<AgentAccountVo>> getAgentAccountList();

    /**
     * 提交结算
     *
     * @param submitSettlementDto
     */
    void submitSettlement(SubmitSettlementDto submitSettlementDto);


    /**
     * 对账单详情
     *
     * @param id
     * @return
     */
    SettlementBillDetailVo getSettlementBillDetail(Long id);

    /**
     * 获取对账单列表
     *
     * @return
     */
    List<SettlementBill> getSettlementBillList();

    /**
     * 历史账单列表
     *
     * @param startDate
     * @param endDate
     * @return
     */
    List<HistoricalBillVo> getHistoricalBillList(Date startDate, Date endDate);


    /**
     * 对账单下载
     * @param id
     * @param response
     */
    void downloadSettlementBill(Long id, HttpServletResponse response);

}
