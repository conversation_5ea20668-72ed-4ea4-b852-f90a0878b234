package com.insurance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.insurance.dto.file.AppFileCenter;
import com.insurance.entity.MediaAndAttached;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author:Oliver
 * @Date: 2025/6/19
 * @Version 1.0
 * @apiNote:
 */
public interface MediaAndAttachedService extends IService<MediaAndAttached> {

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    MediaAndAttached uploadAttached(MultipartFile file,Long id);


    /**
     * 下载文件
     *
     * @param appFileCenter
     * @param response
     */
    public void downloadFile(AppFileCenter appFileCenter, HttpServletResponse response);
}
