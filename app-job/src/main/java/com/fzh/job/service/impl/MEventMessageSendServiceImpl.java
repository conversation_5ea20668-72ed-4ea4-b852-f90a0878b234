package com.fzh.job.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fzh.job.config.TemplateConfig;
import com.fzh.job.config.WechatApplet;
import com.fzh.job.dto.BaseParamDto;
import com.fzh.job.mapper.LogEventPartnerUserAppointmentSenderMapper;
import com.fzh.job.mapper.MEventRegistrationAgentMapper;
import com.fzh.job.mapper.REventPartnerUserAppointmentMapper;
import com.fzh.job.util.HttpClientUtils;
import com.fzh.job.util.WechatAppletUtil;
import com.partner.entity.EventEntity;
import com.partner.entity.LogEventPartnerUserAppointmentSenderEntity;
import com.partner.entity.MEventRegistrationAgentEntity;
import com.partner.entity.REventPartnerUserAppointmentEntity;
import com.partner.util.MyDateUtils;
import com.partner.vo.job.UserMEventVo;
import com.partner.wechat.Template.BaseValue;
import com.partner.wechat.Template.LiveMessageTemplate;
import com.partner.wechat.Template.MeventMessageTemplate;
import com.partner.wechat.params.SendMessageParams;
import com.partner.wechat.result.SendMessageResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.fzh.job.service.MEventMessageSendService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MEventMessageSendServiceImpl implements MEventMessageSendService {
    private static final Logger log = LoggerFactory.getLogger(MEventMessageSendServiceImpl.class);
    @Resource
    private WechatApplet wechatApplet;
    @Resource
    private MEventRegistrationAgentMapper mEventRegistrationAgentMapper;
    @Resource
    private LogEventPartnerUserAppointmentSenderMapper logEventPartnerUserAppointmentSenderMapper;
    @Resource
    private REventPartnerUserAppointmentMapper reventPartnerUserAppointmentMapper;

    @Override
    public Boolean sendEventMessage(BaseParamDto paramDto) {
        Boolean flag=true;


        if(wechatApplet.getSendMessageUrl()==null || wechatApplet.getSendMessageUrl().trim().equals("")){
            log.info("活动消息推送失败:未找到推送URL");
            return Boolean.FALSE;
        }

        List<TemplateConfig> wechatAppletArr= wechatApplet.getTemplateArr();
        TemplateConfig config;
        if(wechatAppletArr==null||wechatAppletArr.size()==0){
            log.info("活动消息推送失败:未找到消息模板");
            return Boolean.FALSE;
        }else {
            Map<String,TemplateConfig> resultmap= wechatAppletArr.stream().collect(Collectors.toMap(TemplateConfig::getCode, o->o));
            config=resultmap.get("partnerMevent");
            if(config==null){
                log.info("活动消息推送失败:未找到消息模板");
                return Boolean.FALSE;
            }
            if(config.getTemplateId()==null|| "".equals(config.getTemplateId()) ){
                log.info("活动消息推送失败:未找到消息模板");
                return Boolean.FALSE;
            }
        }



        List<EventEntity> eventList= mEventRegistrationAgentMapper.selectDetail();
        if(ObjectUtil.isNotEmpty(eventList)){
            try{
                //登录小程序获取token
                String token= WechatAppletUtil.getToken(wechatApplet);//不刷新机制获取token，使用微信token过期自动获取去新token
                if(StringUtils.isEmpty(token)){
                    log.info("活动消息推送失败:获取token失败");
                    return Boolean.FALSE;
                }
                for(EventEntity event:eventList){

                    List<UserMEventVo> userMEventVos= mEventRegistrationAgentMapper.selectDetailList(event.getId());
                    List<LogEventPartnerUserAppointmentSenderEntity> listEventAppointment=new ArrayList<>();

                    //发送消息
                    for(UserMEventVo userMEventVo:userMEventVos){
                        try{
                            REventPartnerUserAppointmentEntity rEventPartnerUserAppointmentEntity=new REventPartnerUserAppointmentEntity();
                            rEventPartnerUserAppointmentEntity.setFkEventRegistrationAgentId(userMEventVo.getId());
                            rEventPartnerUserAppointmentEntity.setFkPartnerUserId(userMEventVo.getFkPartnerUserId());
                            rEventPartnerUserAppointmentEntity.setGmtCreate(LocalDateTime.now());
                            rEventPartnerUserAppointmentEntity.setGmtCreateUser("system");



                            LogEventPartnerUserAppointmentSenderEntity logevent=new LogEventPartnerUserAppointmentSenderEntity();

                            logevent.setFkEventRegistrationAgentId(event.getId());
                            logevent.setFkPartnerUserId(userMEventVo.getFkPartnerUserId());
                            logevent.setGmtCreate(LocalDateTime.now());
                            logevent.setGmtCreateUser("system");

                            if(StringUtils.isEmpty(userMEventVo.getTouser())){
                                //加载日志
                                logevent.setStatus(2);//发送失败
                                logevent.setRemark("用户openId为空");
                                listEventAppointment.add(logevent);

                                rEventPartnerUserAppointmentEntity.setStatus(2);
                                reventPartnerUserAppointmentMapper.insert(rEventPartnerUserAppointmentEntity);

                                continue;
                            }

                            Map headerParams = new HashMap();
                            headerParams.put("Content-Type", "application/json");
                            SendMessageParams sendMessageParams = new SendMessageParams();
                            sendMessageParams.setTemplate_id(config.getTemplateId());
                            sendMessageParams.setPage("/pages/index/index");
                            sendMessageParams.setTouser(userMEventVo.getTouser());

                            MeventMessageTemplate messageTemplate = new MeventMessageTemplate();

                            BaseValue thingValue = new BaseValue();
                            thingValue.setValue(userMEventVo.getTitle()==null?"华通伙伴活动邀约！":userMEventVo.getTitle());
                            messageTemplate.setThing1(thingValue);
                            BaseValue timeValue = new BaseValue();
                            timeValue.setValue(MyDateUtils.formatDate(userMEventVo.getEventTime()));
                            messageTemplate.setTime2(timeValue);

                            BaseValue thing3Value = new BaseValue();
                            thing3Value.setValue(userMEventVo.getAreaCountryName()+userMEventVo.getAreaStateName()+userMEventVo.getAreaCityName());
                            messageTemplate.setThing3(thing3Value);

                            BaseValue thing4Value = new BaseValue();
                            thing4Value.setValue(userMEventVo.getRemark());
                            messageTemplate.setThing4(thing4Value);

                            sendMessageParams.setData(messageTemplate);

                            String sendMessageUrl=wechatApplet.getSendMessageUrl()+"?access_token="+token;
                            String messageReponse= HttpClientUtils.sendMessagePostRequest(sendMessageUrl,headerParams,sendMessageParams);

                            SendMessageResult messageResult = JSON.parseObject(messageReponse, SendMessageResult.class);
                            if(messageResult.getErrcode()==0){
                                //成功  -更新状态
                                logevent.setStatus(1);//发送失败
                                logevent.setRemark(messageReponse);
                                listEventAppointment.add(logevent);

                                rEventPartnerUserAppointmentEntity.setStatus(1);
                                reventPartnerUserAppointmentMapper.insert(rEventPartnerUserAppointmentEntity);
                            }else{
                                //失败 -更新状态
                                logevent.setStatus(2);//发送失败
                                logevent.setRemark(messageReponse);
                                listEventAppointment.add(logevent);
                                rEventPartnerUserAppointmentEntity.setStatus(2);
                                reventPartnerUserAppointmentMapper.insert(rEventPartnerUserAppointmentEntity);
                            }

                        }catch (Exception e){
                            log.info("活动消息推送失败:{};失败人员:{}", e.getMessage(), userMEventVo.getFkPartnerUserId());
                        }
                    }
                    //保存请求日志
                    logEventPartnerUserAppointmentSenderMapper.insert(listEventAppointment);
                }
            }catch (Exception e){
                log.error(e.getMessage());
            }

        }




        return flag;
    }
}
